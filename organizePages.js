const fs = require('fs');
const path = require('path');

// Define the source directory where your current HTML files are located
const sourceDir = path.join(__dirname, 'pages');

// Define the target directories
const targetDirs = {
    decision: path.join(__dirname, 'decision'),
    generators: path.join(__dirname, 'generators'),
};

// Create target directories if they don't exist
for (const dir of Object.values(targetDirs)) {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir);
    }
}

// Move files to the appropriate directories based on their names
fs.readdir(sourceDir, (err, files) => {
    if (err) {
        console.error('Error reading source directory:', err);
        return;
    }

    files.forEach(file => {
        const filePath = path.join(sourceDir, file);
        const ext = path.extname(file);

        // Check if the file is an HTML file
        if (ext === '.html') {
            if (file === 'dice-roller.html' || 
                file === 'flip-a-coin.html' || 
                file === 'spin-the-wheel.html' || 
                file === 'yes-no-coin-flip.html') {
                // Move to decision directory
                fs.rename(filePath, path.join(targetDirs.decision, file), (err) => {
                    if (err) console.error(`Error moving ${file}:`, err);
                });
            } else if (file.includes('random')) {
                // Move to generators directory
                fs.rename(filePath, path.join(targetDirs.generators, file), (err) => {
                    if (err) console.error(`Error moving ${file}:`, err);
                });
            }
        }
    });
});