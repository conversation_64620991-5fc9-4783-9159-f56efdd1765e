<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us | RandomlyGenerate</title>
    <meta name="description" content="Get in touch with <PERSON><PERSON><PERSON><PERSON><PERSON>. We're here to help with your questions, feedback, and suggestions about our random generation tools.">
    <meta name="keywords" content="contact randomlygenerate, feedback, support, help, contact form">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
</head>
<body class="flex flex-col min-h-screen">
    <header-component></header-component>

    <main class="container mx-auto px-4 py-8 flex-grow">
        <div class="max-w-2xl mx-auto">
            <article class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-6">
                    <h1 class="text-3xl font-bold mb-4">Contact Us</h1>
                    <p class="text-gray-600 mb-8">Have questions or suggestions? We'd love to hear from you!</p>

                    <!-- Contact Form -->
                    <form id="contactForm" class="space-y-6">
                        <!-- Name Field -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                            <input 
                                type="text" 
                                id="name" 
                                name="name" 
                                required
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Your name"
                            >
                        </div>

                        <!-- Email Field -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input 
                                type="email" 
                                id="email" 
                                name="email" 
                                required
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                placeholder="<EMAIL>"
                            >
                        </div>

                        <!-- Subject Field -->
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                            <select 
                                id="subject" 
                                name="subject" 
                                required
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="">Select a subject</option>
                                <option value="general">General Inquiry</option>
                                <option value="feedback">Feedback</option>
                                <option value="bug">Report a Bug</option>
                                <option value="feature">Feature Request</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <!-- Message Field -->
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Message</label>
                            <textarea 
                                id="message" 
                                name="message" 
                                required
                                rows="6"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Your message here..."
                            ></textarea>
                        </div>

                        <!-- Submit Button -->
                        <button 
                            type="submit"
                            class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition duration-300"
                        >
                            Send Message
                        </button>
                    </form>

                    <!-- Success Message (hidden by default) -->
                    <div id="successMessage" class="hidden mt-6 p-4 bg-green-100 text-green-700 rounded-lg">
                        Thank you for your message! We'll get back to you soon.
                    </div>

                    <!-- Error Message (hidden by default) -->
                    <div id="errorMessage" class="hidden mt-6 p-4 bg-red-100 text-red-700 rounded-lg">
                    </div>
                </div>
            </article>

            <!-- Additional Contact Information -->
            <div class="mt-12 grid md:grid-cols-2 gap-6">
                <div class="bg-[#e8f4f8] p-6 rounded-lg">
                    <h2 class="font-bold text-lg mb-3">Email Us</h2>
                    <p class="text-gray-600"><EMAIL></p>
                </div>
                <div class="bg-[#e8f4f8] p-6 rounded-lg">
                    <h2 class="font-bold text-lg mb-3">Response Time</h2>
                    <p class="text-gray-600">We typically respond within 24-48 hours.</p>
                </div>
            </div>
        </div>
    </main>

    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>
    <script src="/assets/js/addStructuredData.js"></script>

    <!-- Form Handling Script -->
    <script>
        document.getElementById('contactForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Get form elements
            const form = e.target;
            const successMessage = document.getElementById('successMessage');
            const errorMessage = document.getElementById('errorMessage');
            const submitButton = form.querySelector('button[type="submit"]');
            
            // Hide any existing messages
            successMessage.classList.add('hidden');
            errorMessage.classList.add('hidden');
            
            // Disable submit button and show loading state
            submitButton.disabled = true;
            submitButton.textContent = 'Sending...';
            
            try {
                // Collect form data
                const formData = new FormData(form);
                const data = Object.fromEntries(formData.entries());
                
                // Send to Cloudflare Worker
                const response = await fetch('https://randomlygenerate-resendapi-151124.gattr.workers.dev', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        to: '<EMAIL>',
                        subject: data.subject,
                        message: data.message,
                        name: data.name,
                        email: data.email
                    })
                });
                
                if (response.ok) {
                    // Show success message
                    form.reset();
                    successMessage.classList.remove('hidden');
                    
                    // Hide success message after 5 seconds
                    setTimeout(() => {
                        successMessage.classList.add('hidden');
                    }, 5000);
                } else {
                    throw new Error('Failed to send message');
                }
            } catch (error) {
                // Show error message
                errorMessage.textContent = 'Sorry, there was an error sending your message. Please try again later.';
                errorMessage.classList.remove('hidden');
            } finally {
                // Reset button state
                submitButton.disabled = false;
                submitButton.textContent = 'Send Message';
            }
        });

        // Basic form validation
        const form = document.getElementById('contactForm');
        const inputs = form.querySelectorAll('input, textarea, select');

        inputs.forEach(input => {
            input.addEventListener('invalid', (e) => {
                e.preventDefault();
                input.classList.add('border-red-500');
            });

            input.addEventListener('input', () => {
                if (input.validity.valid) {
                    input.classList.remove('border-red-500');
                }
            });
        });
    </script>
</body>
</html>
