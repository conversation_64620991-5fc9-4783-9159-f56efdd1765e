export default {
  async fetch(request, env) {
    // Handle CORS preflight requests
    if (request.method === "OPTIONS") {
      return new Response(null, {
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization",
        }
      });
    }

    // Only allow POST requests
    if (request.method !== "POST") {
      return new Response("Method not allowed", { status: 405 });
    }

    try {
      const requestData = await request.json();
      const { prompt, imageUrl } = requestData;

      // Prepare message content array
      const content = [
        {
          "type": "text",
          "text": prompt
        }
      ];

      // Add image to content if provided
      if (imageUrl) {
        content.push({
          "type": "image_url",
          "image_url": {
            "url": imageUrl
          }
        });
      }

      // Prepare the OpenRouter request
      const openRouterResponse = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${env.OPENROUTER_API_KEY}`,
          "HTTP-Referer": "https://randomlygenerate.com",
          "X-Title": "RandomlyGenerate Tools",
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          "model": "google/gemini-flash-1.5-8b",
          "messages": [
            {
              "role": "user",
              "content": content
            }
          ]
        })
      });

      // Parse the OpenRouter response
      const data = await openRouterResponse.json();

      if (!openRouterResponse.ok) {
        throw new Error(data.error?.message || 'API request failed');
      }

      // Extract the generated content
      const result = data.choices[0].message.content;

      // Return the response
      return new Response(JSON.stringify({
        success: true,
        result: result
      }), {
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*"
        }
      });

    } catch (error) {
      console.error('Worker error:', error);
      return new Response(JSON.stringify({
        success: false,
        error: error.message || 'An error occurred'
      }), {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*"
        }
      });
    }
  }
}; 
