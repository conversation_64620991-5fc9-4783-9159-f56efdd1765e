<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found | RandomlyGenerate</title>
    <meta name="robots" content="noindex, follow">
    <meta name="description" content="The page you're looking for cannot be found. Try our random generators and tools instead.">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://randomlygenerate.com/404">
    <meta property="og:title" content="Page Not Found | RandomlyGenerate">
    <meta property="og:description" content="The page you're looking for cannot be found. Try our random generators and tools instead.">
    <meta property="og:image" content="https://randomlygenerate.com/assets/images/404-og.jpg">

    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
</head>
<body class="flex flex-col min-h-screen">
    <header-component></header-component>

    <main class="flex-grow container mx-auto px-4 py-16 text-center">
        <h1 class="text-6xl font-bold text-gray-800 mb-4">404</h1>
        <p class="text-xl text-gray-600 mb-8">Page not found</p>
        <p class="text-gray-600 mb-8">The page you're looking for doesn't exist or has been moved.</p>
        <a href="https://randomlygenerate.com" class="inline-block bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
            Go to Homepage
        </a>
        <div class="mt-8">
            <h2 class="text-2xl font-semibold mb-4">Popular Tools:</h2>
            <ul class="list-disc list-inside text-gray-600">
                <li><a href="/generators/random-number" class="text-blue-500">Random Number Generator</a></li>
                <li><a href="/generators/random-password-generator" class="text-blue-500">Random Password Generator</a></li>
                <li><a href="/generators/random-name-generator" class="text-blue-500">Random Name Generator</a></li>
            </ul>
        </div>
    </main>

    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>
</body>
</html> 