<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spin the Wheel | Random Wheel Spinner | FlipACoin</title>
    <meta name="description" content="Create and spin a custom wheel with your own items. Perfect for random selection, decision making, and games.">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
    <style>
        .wheel-container {
            position: relative;
            width: 400px;
            height: 400px;
            margin: 20px auto;
        }

        .wheel {
            width: 100%;
            height: 100%;
            transition: transform 4s cubic-bezier(0.17, 0.67, 0.12, 0.99);
        }

        .spinner {
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 20px solid transparent;
            border-right: 20px solid transparent;
            border-top: 40px solid #3B82F6;
            z-index: 2;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }

        .wheel text {
            font-family: Arial, sans-serif;
            font-weight: bold;
            pointer-events: none;
            dominant-baseline: middle;
        }

        @media (max-width: 640px) {
            .wheel-container {
                width: 300px;
                height: 300px;
            }

            .wheel text {
                font-size: 2.5px;
            }
        }
    </style>
</head>
<body class="flex flex-col min-h-screen">
    <header-component></header-component>

    <main class="flex-grow container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto text-center">
            <h1 class="text-4xl font-bold mb-8">Spin the Wheel</h1>

            <!-- Wheel Section -->
            <div class="wheel-container">
                <div class="spinner"></div>
                <svg class="wheel" viewBox="0 0 100 100">
                    <!-- Wheel segments will be added here by JavaScript -->
                </svg>
            </div>

            <!-- Controls Section -->
            <div class="max-w-md mx-auto mt-8 space-y-4">
                <textarea 
                    id="nameInput" 
                    rows="4" 
                    class="w-full p-4 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter items, one per line&#10;Example:&#10;Pizza&#10;Burger&#10;Sushi&#10;Tacos"></textarea>
                
                <div class="space-x-4">
                    <button onclick="updateWheel()" 
                            class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-300">
                        Update Wheel
                    </button>
                    <button onclick="spin()" 
                            class="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-300">
                        SPIN!
                    </button>
                </div>
            </div>

            <!-- Winner Display -->
            <div id="winner" class="mt-8 text-2xl font-bold text-green-600"></div>
        </div>

        <!-- New SEO Content Sections -->
        <div class="max-w-4xl mx-auto mt-16 bg-[#e8f4f8] rounded-lg p-8">
            <!-- About Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">About Wheel Spinning</h2>
                <p class="text-gray-600 leading-relaxed">
                    Wheel spinning is a dynamic method of random selection that combines visual engagement with fair decision-making. This interactive tool allows users to create customized wheels with their own choices, making it perfect for group decisions, classroom activities, contest drawings, and team building exercises. Unlike simple random selectors, the spinning wheel provides an exciting visual experience that builds anticipation and ensures transparency in the selection process, making it ideal for both professional and casual settings.
                </p>
            </section>

            <!-- History Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">History of Wheel Spinning</h2>
                <p class="text-gray-600 leading-relaxed">
                    The concept of spinning wheels for decision-making dates back to ancient civilizations, with early versions appearing in medieval European gambling houses and carnival games. The most famous adaptation, the Wheel of Fortune, emerged in the 18th century as a symbol of fate and chance. The modern prize wheel gained popularity in the mid-20th century through television game shows, making it a cultural icon for random selection and entertainment. Today, digital wheel spinners have revolutionized this concept, offering customizable, fair, and accessible solutions for various applications while maintaining the excitement of the traditional spinning wheel experience.
                </p>
            </section>

            <!-- How to Use Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">How to Use Our Wheel Spinner</h2>
                <div class="text-gray-600 space-y-4">
                    <p><strong>1. Enter Your Items:</strong> Type or paste your options into the text box, with one item per line</p>
                    <p><strong>2. Create Your Wheel:</strong> Click the "Update Wheel" button to generate your custom wheel</p>
                    <p><strong>3. Spin the Wheel:</strong> Click the "SPIN!" button to start the animation</p>
                    <p><strong>4. View Results:</strong> Wait for the wheel to stop and see your randomly selected winner</p>
                    
                    <p><strong>Tips for Best Results:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Enter at least 2 items for the wheel to function</li>
                        <li>Keep item names concise for better visibility</li>
                        <li>Wait for the spinning animation to complete before spinning again</li>
                        <li>Use clear, distinct options to avoid confusion</li>
                    </ul>
                </div>
            </section>

            <!-- FAQ Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">Wheel Spinner FAQ</h2>
                <div class="space-y-6">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Is the wheel spinner truly random?</h3>
                        <p class="text-gray-600">Yes, our wheel spinner uses cryptographic-grade random number generation to ensure completely fair and unbiased results for each spin.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">How many items can I add to the wheel?</h3>
                        <p class="text-gray-600">While there's no strict limit, we recommend 2-20 items for optimal visibility and user experience. Each item will be clearly displayed on the wheel.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I save my custom wheel?</h3>
                        <p class="text-gray-600">Currently, wheels are session-based. We recommend keeping a copy of your items list if you plan to use the same options frequently.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Does the animation affect the outcome?</h3>
                        <p class="text-gray-600">No, the spinning animation is purely visual. The result is determined by our random number generator, independent of the animation.</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>

    <script>
        let isSpinning = false;
        let currentRotation = 0;
        let names = [];

        function updateWheel() {
            names = nameInput.value.split('\n').filter(name => name.trim() !== '');
            if (names.length < 2) {
                alert('Please enter at least 2 items!');
                return;
            }
            
            const segmentAngle = 360 / names.length;
            const wheel = document.querySelector('.wheel');
            wheel.innerHTML = '';
            
            names.forEach((name, index) => {
                const startAngle = index * segmentAngle;
                const endAngle = (index + 1) * segmentAngle;
                
                const start = polarToCartesian(50, 50, 50, startAngle);
                const end = polarToCartesian(50, 50, 50, endAngle);
                
                const largeArcFlag = segmentAngle <= 180 ? 0 : 1;
                
                const pathData = [
                    'M', 50, 50,
                    'L', start.x, start.y,
                    'A', 50, 50, 0, largeArcFlag, 1, end.x, end.y,
                    'Z'
                ].join(' ');
                
                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                path.setAttribute('d', pathData);
                path.setAttribute('fill', `hsl(${index * (360 / names.length)}, 70%, 50%)`);
                wheel.appendChild(path);
                
                // Updated text positioning for horizontal text
                const textAngle = startAngle + (segmentAngle / 2);
                const textRadius = 30; // Distance from center
                const textPos = polarToCartesian(50, 50, textRadius, textAngle);
                
                // Create text path for curved text
                const textPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                const textPathId = `textPath${index}`;
                textPath.setAttribute('id', textPathId);
                textPath.setAttribute('d', `M ${textPos.x} ${textPos.y} h 20`); // Horizontal line for text
                wheel.appendChild(textPath);
                
                const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                text.setAttribute('fill', 'white');
                text.setAttribute('font-size', '3px');
                text.setAttribute('font-weight', 'bold');
                text.setAttribute('text-anchor', 'middle');
                
                // Rotate the text to be horizontal but keep it within the segment
                const rotationAngle = textAngle;
                text.setAttribute('transform', `rotate(${rotationAngle}, ${textPos.x}, ${textPos.y})`);
                
                // Handle long text
                const maxLength = 12;
                const displayText = name.length > maxLength ? name.substring(0, maxLength) + '...' : name;
                text.textContent = displayText;
                
                // Position text
                text.setAttribute('x', textPos.x);
                text.setAttribute('y', textPos.y);
                
                // Add text shadow and other styles
                text.setAttribute('style', `
                    text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
                    dominant-baseline: middle;
                `);
                
                wheel.appendChild(text);
            });
        }

        function polarToCartesian(centerX, centerY, radius, angleInDegrees) {
            const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
            return {
                x: centerX + (radius * Math.cos(angleInRadians)),
                y: centerY + (radius * Math.sin(angleInRadians))
            };
        }

        function spin() {
            if (isSpinning || names.length < 2) return;
            
            isSpinning = true;
            document.getElementById('winner').textContent = '';
            
            const extraSpins = 5;
            const targetRotation = currentRotation + (360 * extraSpins) + Math.random() * 360;
            
            document.querySelector('.wheel').style.transform = `rotate(${targetRotation}deg)`;
            currentRotation = targetRotation;
            
            setTimeout(() => {
                isSpinning = false;
                
                // Calculate winner
                const finalAngle = targetRotation % 360;
                const segmentAngle = 360 / names.length;
                const winnerIndex = Math.floor((360 - finalAngle) / segmentAngle);
                const winner = names[winnerIndex % names.length];
                
                document.getElementById('winner').textContent = `Winner: ${winner}!`;
            }, 4000);
        }

        // Initialize with sample items
        document.getElementById('nameInput').value = 'Pizza\nBurger\nSushi\nTacos';
        updateWheel();
    </script>
</body>
</html> 