<!DOCTYPE html>
<html lang="en"> 
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> Roller | Multiple Dice Rolling Simulator | FlipACoin</title>
    <meta name="description" content="Roll up to 6 dice simultaneously with our virtual dice roller. Customizable dice colors and instant results. Perfect for games and decision making.">
    <meta name="keywords" content="dice roller, roll dice, virtual dice, online dice, multiple dice, dice simulator, random dice roll">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
    <style>
        .dice-container {
            perspective: 1000px;
            margin: 10px;
            display: inline-block;
        }

        .dice {
            width: 80px;
            height: 80px;
            position: relative;
            transform-style: preserve-3d;
            transition: transform 1.5s ease-out;
        }

        .dice.rolling {
            animation: roll 1.5s ease-out;
        }

        @keyframes roll {
            0% { transform: rotateX(0) rotateY(0) rotateZ(0); }
            100% { transform: rotateX(720deg) rotateY(720deg) rotateZ(720deg); }
        }

        .dice-face {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 10px;
            border: 2px solid #fff;
            display: grid;
            grid-template: repeat(3, 1fr) / repeat(3, 1fr);
            padding: 8px;
            background: #FF6B6B;
            box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .dot {
            background-color: transparent;
            border-radius: 50%;
            margin: 2px;
        }

        .front  { transform: translateZ(40px); }
        .back   { transform: translateZ(-40px) rotateY(180deg); }
        .right  { transform: translateX(40px) rotateY(90deg); }
        .left   { transform: translateX(-40px) rotateY(-90deg); }
        .top    { transform: translateY(-40px) rotateX(90deg); }
        .bottom { transform: translateY(40px) rotateX(-90deg); }

        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            border: 3px solid transparent;
            transition: transform 0.2s ease;
        }

        .color-option:hover {
            transform: scale(1.1);
        }

        .color-option.active {
            border-color: #333;
        }

        /* Dot Patterns */
        .one .dot:nth-child(5),
        .two .dot:nth-child(1), .two .dot:nth-child(9),
        .three .dot:nth-child(1), .three .dot:nth-child(5), .three .dot:nth-child(9),
        .four .dot:nth-child(1), .four .dot:nth-child(3), .four .dot:nth-child(7), .four .dot:nth-child(9),
        .five .dot:nth-child(1), .five .dot:nth-child(3), .five .dot:nth-child(5), .five .dot:nth-child(7), .five .dot:nth-child(9),
        .six .dot:nth-child(1), .six .dot:nth-child(3), .six .dot:nth-child(4), .six .dot:nth-child(6), .six .dot:nth-child(7), .six .dot:nth-child(9) {
            background-color: #fff;
        }

        #dice-area {
            min-height: 200px;
        }

        .total-display {
            font-size: 2.5rem;
            font-weight: bold;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .total-display.show {
            opacity: 1;
        }
    </style>
</head>
<body class="flex flex-col min-h-screen">
    <header-component></header-component>

    <main class="flex-grow container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto text-center">
            <h1 class="text-4xl font-bold mb-8">Dice Roller</h1>

            <!-- Controls Section -->
            <div class="mb-8 space-y-6">
                <!-- Dice Selection -->
                <div>
                    <label for="diceCount" class="block text-sm font-medium text-gray-700 mb-2">Number of Dice</label>
                    <select id="diceCount" 
                            class="w-32 px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                        <option value="1">1 Die</option>
                        <option value="2">2 Dice</option>
                        <option value="3">3 Dice</option>
                        <option value="4">4 Dice</option>
                        <option value="5">5 Dice</option>
                        <option value="6">6 Dice</option>
                    </select>
                </div>

                <!-- Color Options -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Choose Dice Color</h3>
                    <div class="flex justify-center gap-4">
                        <div class="color-option active" style="background-color: #FF6B6B" onclick="changeDiceColor('#FF6B6B')"></div>
                        <div class="color-option" style="background-color: #4ECDC4" onclick="changeDiceColor('#4ECDC4')"></div>
                        <div class="color-option" style="background-color: #45B7D1" onclick="changeDiceColor('#45B7D1')"></div>
                        <div class="color-option" style="background-color: #96CEB4" onclick="changeDiceColor('#96CEB4')"></div>
                        <div class="color-option" style="background-color: #D4A5A5" onclick="changeDiceColor('#D4A5A5')"></div>
                    </div>
                </div>
            </div>

            <!-- Dice Display Area -->
            <div id="dice-area" class="mb-8 flex flex-wrap justify-center items-center gap-4">
                <!-- Dice will be dynamically inserted here -->
            </div>

            <!-- Total Display -->
            <div class="total-display mb-8" id="total">Total: 0</div>

            <!-- Roll Button -->
            <button onclick="rollDice()" 
                    class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-4 px-8 rounded-lg text-xl transition duration-300">
                ROLL DICE!
            </button>
        </div>

        <!-- New SEO Content Sections -->
        <div class="max-w-4xl mx-auto mt-16 bg-[#e8f4f8] rounded-lg p-8">
            <!-- About Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">About Dice Rolling</h2>
                <p class="text-gray-600 leading-relaxed">
                    Dice rolling is an ancient practice of chance and probability that has shaped gaming, gambling, and decision-making throughout human history. From simple six-sided dice (d6) to complex polyhedral dice used in tabletop RPGs, dice rolling remains a fundamental mechanic in board games, role-playing games (RPGs), educational activities, and statistical modeling. The act of rolling dice creates an element of randomness and excitement, making it essential for games like Yahtzee, Dungeons & Dragons, Monopoly, and countless other tabletop games. Modern technology has evolved this practice into digital formats, offering convenient alternatives while maintaining the traditional thrill of chance-based outcomes.
                </p>
            </section>

            <!-- History Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">History of Dice Rolling</h2>
                <p class="text-gray-600 leading-relaxed">
                    Dice rolling dates back over 5,000 years, with the earliest dice discovered in ancient civilizations like Mesopotamia, Egypt, and the Indus Valley. Originally carved from bones, stones, and ivory, these early dice were used for gambling, divination, and religious ceremonies. The standard six-sided die we know today was standardized during the Roman Empire, though various cultures developed their own unique dice shapes and counting systems. The medieval period saw dice games flourish across Europe, despite occasional religious and legal restrictions on gambling. The 20th century brought a revolution with the emergence of tabletop role-playing games, introducing specialized polyhedral dice (d4, d8, d10, d12, d20) and establishing dice rolling as a cornerstone of modern gaming culture. Today, dice rolling continues to evolve through both traditional physical dice and digital implementations, maintaining its significance in gaming, probability theory, and random number generation.
                </p>
            </section>

            <!-- How to Use Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">How to Use Our Dice Roller</h2>
                <div class="text-gray-600 space-y-4">
                    <p><strong>1. Select Dice Count:</strong> Choose the number of dice you want to roll (1-6) using the dropdown menu at the top.</p>
                    <p><strong>2. Customize Appearance:</strong> Click on any of the color options to change the dice color to your preference.</p>
                    <p><strong>3. Roll the Dice:</strong> Click the "ROLL DICE!" button to initiate the roll animation.</p>
                    <p><strong>4. View Results:</strong> Watch the 3D animation and see your total score displayed below the dice.</p>
                    <p><strong>Tips:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Wait for the rolling animation to complete before rolling again</li>
                        <li>Use multiple dice for games requiring higher number ranges</li>
                        <li>The total is automatically calculated for your convenience</li>
                    </ul>
                </div>
            </section>

            <!-- FAQ Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">Digital Dice Rolling FAQ</h2>
                <div class="space-y-6">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">How random is an online dice roller?</h3>
                        <p class="text-gray-600">Our online dice roller uses cryptographic-grade random number generation, providing results that are statistically as random as physical dice, if not more so.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I use this dice roller for serious games?</h3>
                        <p class="text-gray-600">Yes! Our dice roller is suitable for all types of games, from casual fun to serious gaming sessions, providing fair and random results every time.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Why use an online dice roller instead of physical dice?</h3>
                        <p class="text-gray-600">Online dice rollers offer convenience, perfect randomization, instant result calculation, and are ideal for remote gaming sessions or when physical dice aren't available.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Does the animation affect the randomness?</h3>
                        <p class="text-gray-600">No, the 3D animation is purely visual. The random number is generated independently using our secure random number generator.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I roll multiple dice at once?</h3>
                        <p class="text-gray-600">Yes, you can roll up to 6 dice simultaneously, with the total automatically calculated for your convenience.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Will I get the same results on different devices?</h3>
                        <p class="text-gray-600">The results are completely random regardless of the device you're using, ensuring fair and unbiased rolls across all platforms.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Is there a limit to how many times I can roll?</h3>
                        <p class="text-gray-600">No, you can roll the dice as many times as you need. Just wait for each animation to complete before rolling again.</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>

    <script>
        let isRolling = false;

        function createDice() {
            return `
                <div class="dice-container">
                    <div class="dice">
                        <div class="dice-face front">
                            ${Array(9).fill('<div class="dot"></div>').join('')}
                        </div>
                        <div class="dice-face back">
                            ${Array(9).fill('<div class="dot"></div>').join('')}
                        </div>
                        <div class="dice-face right">
                            ${Array(9).fill('<div class="dot"></div>').join('')}
                        </div>
                        <div class="dice-face left">
                            ${Array(9).fill('<div class="dot"></div>').join('')}
                        </div>
                        <div class="dice-face top">
                            ${Array(9).fill('<div class="dot"></div>').join('')}
                        </div>
                        <div class="dice-face bottom">
                            ${Array(9).fill('<div class="dot"></div>').join('')}
                        </div>
                    </div>
                </div>
            `;
        }

        function updateDiceCount() {
            const count = parseInt(document.getElementById('diceCount').value);
            const diceArea = document.getElementById('dice-area');
            diceArea.innerHTML = Array(count).fill(createDice()).join('');
            document.getElementById('total').textContent = 'Total: 0';
            document.getElementById('total').classList.remove('show');
        }

        function changeDiceColor(color) {
            const faces = document.querySelectorAll('.dice-face');
            faces.forEach(face => {
                face.style.backgroundColor = color;
            });
            
            document.querySelectorAll('.color-option').forEach(option => {
                option.classList.remove('active');
            });
            document.querySelector(`[onclick="changeDiceColor('${color}')"]`).classList.add('active');
        }

        function rollDice() {
            if (isRolling) return;
            
            isRolling = true;
            const diceElements = document.querySelectorAll('.dice');
            const totalDisplay = document.getElementById('total');
            totalDisplay.classList.remove('show');
            
            diceElements.forEach(dice => {
                dice.classList.add('rolling');
            });
            
            setTimeout(() => {
                let total = 0;
                diceElements.forEach(dice => {
                    dice.classList.remove('rolling');
                    const value = Math.floor(Math.random() * 6) + 1;
                    total += value;
                    
                    // Update dice face classes
                    const faces = dice.querySelectorAll('.dice-face');
                    faces.forEach(face => {
                        face.classList.remove('one', 'two', 'three', 'four', 'five', 'six');
                        face.classList.add(['one', 'two', 'three', 'four', 'five', 'six'][value - 1]);
                    });
                });
                
                totalDisplay.textContent = `Total: ${total}`;
                totalDisplay.classList.add('show');
                isRolling = false;
            }, 1500);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            updateDiceCount();
            document.getElementById('diceCount').addEventListener('change', updateDiceCount);
        });
    </script>
</body>
</html> 