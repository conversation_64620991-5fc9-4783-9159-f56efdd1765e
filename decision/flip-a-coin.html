<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Virtual Coin Flip Generator | Online Coin Flip Simulator | FlipACoin</title>
    <meta name="description" content="Use our free online coin flip generator for instant random decisions. Virtual coin toss simulator with customizable coins. Perfect for quick yes/no choices.">
    <meta name="keywords" content="flip a coin, coin flip, google coin flip, flip coin, coin flip generator, virtual coin flip, online coin flip, random coin flip, head or tails">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
    <style>
        .coin {
            width: 200px;
            height: 200px;
            margin: 2rem auto;
            position: relative;
            transition: transform 1s ease-in-out;
            transform-style: preserve-3d;
        }

        .coin.flipping {
            animation: flip 1s ease-in-out;
        }

        @keyframes flip {
            0% { transform: rotateY(0); }
            100% { transform: rotateY(1800deg); }
        }

        .coin-face {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            backface-visibility: hidden;
            border: 8px solid #e0e0e0;
        }

        .heads {
            background: #fff;
            transform: rotateY(0deg);
        }

        .tails {
            background: #fff;
            transform: rotateY(180deg);
        }

        .result {
            margin-top: 1rem;
            font-size: 1.5rem;
            font-weight: bold;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .result.show {
            opacity: 1;
        }

        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            border: 3px solid transparent;
            transition: transform 0.2s ease;
        }

        .color-option:hover {
            transform: scale(1.1);
        }

        .color-option.active {
            border-color: #333;
        }
    </style>
</head>
<body class="flex flex-col min-h-screen">
    <header-component></header-component>

    <main class="flex-grow container mx-auto px-4 py-8">
        <!-- Coin Flip Interface -->
        <div class="max-w-2xl mx-auto text-center">
            <h1 class="text-4xl font-bold mb-8">Flip A Coin</h1>
            
            <div class="coin">
                <div class="coin-face heads">H</div>
                <div class="coin-face tails">T</div>
            </div>

            <div class="result text-gray-800" id="result"></div>

            <div class="mt-8">
                <button onclick="flipCoin()" 
                        class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-4 px-8 rounded-lg text-xl transition duration-300">
                    FLIP!
                </button>
            </div>

            <div class="flex gap-4 justify-center mt-8">
                <div class="color-option active" style="background: #FF6B6B" onclick="changeCoinColor('#FF6B6B')"></div>
                <div class="color-option" style="background: #4ECDC4" onclick="changeCoinColor('#4ECDC4')"></div>
                <div class="color-option" style="background: #FFE66D" onclick="changeCoinColor('#FFE66D')"></div>
                <div class="color-option" style="background: #95A5A6" onclick="changeCoinColor('#95A5A6')"></div>
                <div class="color-option" style="background: #2ECC71" onclick="changeCoinColor('#2ECC71')"></div>
            </div>
        </div>

        <!-- New SEO Content Sections -->
        <div class="max-w-4xl mx-auto mt-16 bg-[#e8f4f8] rounded-lg p-8">
            <!-- About Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">About Virtual Coin Flipping</h2>
                <p class="text-gray-600 leading-relaxed">
                    Virtual coin flipping is a digital evolution of the traditional coin toss, providing an unbiased method for making binary decisions. This modern approach uses advanced random number generation to ensure fair results, making it ideal for everything from starting sports games to making everyday choices. Unlike physical coins, which can be influenced by various factors, digital coin flips provide truly random outcomes, making them increasingly popular in both casual and professional settings.
                </p>
            </section>

            <!-- History Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">History of Coin Flipping</h2>
                <p class="text-gray-600 leading-relaxed">
                    Coin flipping has been a decision-making tool for thousands of years, dating back to ancient Rome where it was known as "navia aut caput" (ship or head). The Romans used copper coins with the emperor's head on one side and a ship on the other, establishing the foundation for modern heads or tails. Throughout history, different cultures developed their own versions - from the Greeks using "night and day" to Medieval England's "cross and pile." The practice evolved from using shells and basic metal discs to standardized currency, and now into the digital age with virtual coin flippers, maintaining its significance in decision-making across centuries.
                </p>
            </section>

            <!-- How to Use Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">How to Use Our Coin Flip Generator</h2>
                <div class="text-gray-600 space-y-4">
                    <p><strong>1. Start the Flip:</strong> Click the "FLIP!" button to initiate the coin toss animation.</p>
                    <p><strong>2. Customize Appearance:</strong> Choose from five different coin colors using the selector buttons below the coin.</p>
                    <p><strong>3. View Result:</strong> Watch the 3D animation and see your result (HEADS or TAILS) displayed clearly.</p>
                    <p><strong>Tips:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Wait for the flipping animation to complete before flipping again</li>
                        <li>Use the color options to personalize your experience</li>
                        <li>Results are completely random and independent of previous flips</li>
                    </ul>
                </div>
            </section>

            <!-- FAQ Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">Virtual Coin Flipping FAQ</h2>
                <div class="space-y-6">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Is virtual coin flipping truly random?</h3>
                        <p class="text-gray-600">Yes, our coin flip generator uses cryptographic-grade random number generation to ensure completely fair and unbiased results.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">How does it compare to physical coin flips?</h3>
                        <p class="text-gray-600">Virtual flips eliminate physical variables like flip technique, air resistance, and surface conditions, providing more consistent and truly random results.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I use this for official decisions?</h3>
                        <p class="text-gray-600">Yes, our virtual coin flip is suitable for official decisions where a random binary choice is needed, though we recommend checking specific regulations for formal competitions.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Does the animation affect the result?</h3>
                        <p class="text-gray-600">No, the result is determined by the random number generator, not the animation. The animation is purely for visual appeal.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Why choose a virtual coin flip?</h3>
                        <p class="text-gray-600">Virtual coin flips offer convenience, perfect randomness, instant results, and the ability to customize the appearance without affecting the outcome.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Will I get the same results on different devices?</h3>
                        <p class="text-gray-600">The results are completely random regardless of the device you're using, ensuring fair and unbiased flips across all platforms.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Is there a limit to how many times I can flip?</h3>
                        <p class="text-gray-600">No, you can flip the coin as many times as needed. Each flip is completely independent of previous flips.</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>
    
    <script>
        let isFlipping = false;

        function flipCoin() {
            if (isFlipping) return;
            
            // Scroll to the coin element smoothly
            const coinElement = document.querySelector('.coin');
            coinElement.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'center'
            });
            
            isFlipping = true;
            const coin = document.querySelector('.coin');
            const resultDisplay = document.querySelector('#result');
            
            resultDisplay.classList.remove('show');
            coin.classList.add('flipping');
            
            const result = Math.random() < 0.5 ? 'heads' : 'tails';
            
            setTimeout(() => {
                coin.style.transform = `rotateY(${result === 'heads' ? '0' : '180'}deg)`;
                coin.classList.remove('flipping');
                
                resultDisplay.textContent = result.toUpperCase();
                resultDisplay.classList.add('show');
                
                isFlipping = false;
            }, 1000);
        }

        function changeCoinColor(color) {
            const coinFaces = document.querySelectorAll('.coin-face');
            coinFaces.forEach(face => {
                face.style.backgroundColor = color;
            });
            
            document.querySelectorAll('.color-option').forEach(option => {
                option.classList.remove('active');
            });
            document.querySelector(`[onclick="changeCoinColor('${color}')"]`).classList.add('active');
        }

        // Initialize with default color
        changeCoinColor('#FF6B6B');
    </script>
</body>
</html> 
</html> 