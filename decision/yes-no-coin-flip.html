<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yes No Coin Flip | Random Yes or No Decision Maker | FlipACoin</title>
    <meta name="description" content="Make quick yes/no decisions with our virtual coin flip generator. Simple, random, and unbiased way to answer yes/no questions instantly.">
    <meta name="keywords" content="yes no coin flip, flip a coin yes or no, random decision maker, yes no generator, flip a coin decision, yes no randomizer">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
    <style>
        .coin {
            width: 200px;
            height: 200px;
            margin: 2rem auto;
            position: relative;
            transition: transform 1s ease-in-out;
            transform-style: preserve-3d;
        }

        .coin.flipping {
            animation: flip 1s ease-in-out;
        }

        @keyframes flip {
            0% { transform: rotateY(0); }
            100% { transform: rotateY(1800deg); }
        }

        .coin-face {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            backface-visibility: hidden;
            border: 8px solid #e0e0e0;
        }

        .yes {
            background: #fff;
            transform: rotateY(0deg);
        }

        .no {
            background: #fff;
            transform: rotateY(180deg);
        }

        .result {
            margin-top: 1rem;
            font-size: 1.5rem;
            font-weight: bold;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .result.show {
            opacity: 1;
        }

        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            border: 3px solid transparent;
            transition: transform 0.2s ease;
        }

        .color-option:hover {
            transform: scale(1.1);
        }

        .color-option.active {
            border-color: #333;
        }
    </style>
</head>
<body class="flex flex-col min-h-screen">
    <header-component></header-component>

    <main class="flex-grow container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto text-center">
            <h1 class="text-4xl font-bold mb-8">Yes No Coin Flip</h1>
            
            <div class="coin">
                <div class="coin-face yes">YES</div>
                <div class="coin-face no">NO</div>
            </div>

            <div class="result text-gray-800" id="result"></div>

            <div class="mt-8">
                <button onclick="flipCoin()" 
                        class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-4 px-8 rounded-lg text-xl transition duration-300">
                    GET ANSWER!
                </button>
            </div>

            <div class="flex gap-4 justify-center mt-8">
                <div class="color-option active" style="background: #FF6B6B" onclick="changeCoinColor('#FF6B6B')"></div>
                <div class="color-option" style="background: #4ECDC4" onclick="changeCoinColor('#4ECDC4')"></div>
                <div class="color-option" style="background: #FFE66D" onclick="changeCoinColor('#FFE66D')"></div>
                <div class="color-option" style="background: #95A5A6" onclick="changeCoinColor('#95A5A6')"></div>
                <div class="color-option" style="background: #2ECC71" onclick="changeCoinColor('#2ECC71')"></div>
            </div>
        </div>

        <div class="max-w-4xl mx-auto mt-16 bg-[#e8f4f8] rounded-lg p-8">
            <!-- About Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">About Yes/No Decision Making</h2>
                <p class="text-gray-600 leading-relaxed">
                    The Yes/No Coin Flip is a modern digital tool designed for making binary decisions quickly and fairly. This innovative approach combines the traditional concept of coin flipping with advanced random number generation, providing an unbiased method for answering simple yes/no questions. Whether you're making personal choices, group decisions, or need help breaking a deadlock, this tool offers a straightforward and impartial way to reach a conclusion. The digital format ensures perfect 50/50 probability, eliminating the physical biases that might affect traditional coin tosses.
                </p>
            </section>

            <!-- History Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">History of Yes/No Decision Making</h2>
                <p class="text-gray-600 leading-relaxed">
                    Binary decision-making tools have deep historical roots across various cultures. The ancient Chinese used the I Ching for yes/no divination, while Romans relied on simple choice methods for important decisions. Throughout history, different civilizations developed their own versions of binary decision-making tools, from casting lots to flipping coins. The digital age has transformed these traditional methods into precise, random generators while maintaining the simplicity and clarity of a yes/no answer. This evolution represents thousands of years of human innovation in decision-making techniques, culminating in today's perfectly random digital solutions.
                </p>
            </section>

            <!-- How to Use Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">How to Use Our Yes/No Generator</h2>
                <div class="text-gray-600 space-y-4">
                    <p><strong>1. Prepare Your Question:</strong> Form a clear yes/no question in your mind</p>
                    <p><strong>2. Initiate the Flip:</strong> Click the "GET ANSWER!" button to start</p>
                    <p><strong>3. View Your Result:</strong> Wait for the animation to complete and see your answer</p>
                    
                    <p><strong>Customization Options:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Choose from five different coin colors using the color selector buttons</li>
                        <li>Each color option provides a unique visual experience</li>
                        <li>The result display shows your answer clearly below the coin</li>
                    </ul>

                    <p><strong>Best Practices:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Wait for the animation to complete before flipping again</li>
                        <li>Frame your question to require a simple yes/no answer</li>
                        <li>Consider the weight of your decision before relying on random chance</li>
                        <li>Use for appropriate decisions that suit a binary outcome</li>
                    </ul>
                </div>
            </section>

            <!-- FAQ Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">Yes/No Generator FAQ</h2>
                <div class="space-y-6">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Is this truly random?</h3>
                        <p class="text-gray-600">Yes, our generator uses cryptographic-grade random number generation to ensure a perfect 50/50 probability for each result.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Should I use this for important decisions?</h3>
                        <p class="text-gray-600">While suitable for breaking simple deadlocks, we recommend careful consideration and proper decision-making processes for significant life choices.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Do previous results affect future ones?</h3>
                        <p class="text-gray-600">No, each flip is completely independent. Getting several "yes" or "no" answers in a row doesn't affect the next result.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Why use a digital yes/no generator?</h3>
                        <p class="text-gray-600">Digital generators provide perfect randomness, eliminate physical biases, and offer convenient access anytime you need a quick decision.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">How many times can I use it?</h3>
                        <p class="text-gray-600">There's no limit - you can use the generator as many times as needed, with each result being completely random and independent.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Does the animation affect the result?</h3>
                        <p class="text-gray-600">No, the spinning animation is purely visual. The yes/no result is determined instantly by our random number generator.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I trust the results?</h3>
                        <p class="text-gray-600">Our generator provides truly random results with equal probability. However, use it as a tool to aid decision-making rather than the sole deciding factor for important choices.</p>
                    </div>
                </div>
            </section>
        </div>
    </main>
    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>
    
    <script>
        let isFlipping = false;

        function flipCoin() {
            if (isFlipping) return;
            
            const coinElement = document.querySelector('.coin');
            coinElement.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'center'
            });
            
            isFlipping = true;
            const coin = document.querySelector('.coin');
            const resultDisplay = document.querySelector('#result');
            
            resultDisplay.classList.remove('show');
            coin.classList.add('flipping');
            
            const result = Math.random() < 0.5 ? 'YES' : 'NO';
            
            setTimeout(() => {
                coin.style.transform = `rotateY(${result === 'YES' ? '0' : '180'}deg)`;
                coin.classList.remove('flipping');
                
                resultDisplay.textContent = result;
                resultDisplay.classList.add('show');
                
                isFlipping = false;
            }, 1000);
        }

        function changeCoinColor(color) {
            const coinFaces = document.querySelectorAll('.coin-face');
            coinFaces.forEach(face => {
                face.style.backgroundColor = color;
            });
            
            document.querySelectorAll('.color-option').forEach(option => {
                option.classList.remove('active');
            });
            document.querySelector(`[onclick="changeCoinColor('${color}')"]`).classList.add('active');
        }

        // Initialize with default color
        changeCoinColor('#FF6B6B');
    </script>
</body>
</html> 