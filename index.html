<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RandomlyGenerate | Free Random Generators & Decision Making Tools</title>
    <meta name="description" content="Free collection of random generators and decision-making tools. Generate names, passwords, numbers, flip coins, roll dice, and more. Simple, fast, and reliable.">
    <meta name="keywords" content="random generator, decision tools, coin flip, dice roller, random number generator, name generator">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://randomlygenerate.com/">
    <meta property="og:title" content="RandomlyGenerate | Free Random Generators & Decision Tools">
    <meta property="og:description" content="Free collection of random generators and decision-making tools. Generate names, passwords, numbers, and more instantly.">
    <meta property="og:image" content="https://randomlygenerate.com/assets/images/og-image.jpg">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://randomlygenerate.com/">
    <meta name="twitter:title" content="RandomlyGenerate | Free Random Generators & Decision Tools">
    <meta name="twitter:description" content="Free collection of random generators and decision-making tools. Generate names, passwords, numbers, and more instantly.">
    <meta name="twitter:image" content="https://randomlygenerate.com/assets/images/twitter-image.jpg">

    <link rel="canonical" href="https://randomlygenerate.com/">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
</head>
<body class="flex flex-col min-h-screen">
    <header-component></header-component>

    <main class="flex-grow">
        <!-- Hero Section with Search -->
        <section class="bg-white py-12">
            <div class="container mx-auto px-4">
                <h1 class="text-4xl font-bold text-center mb-8">Random Tools Directory</h1>
                <div class="max-w-xl mx-auto">
                    <div class="relative">
                        <input type="text" id="search" 
                            class="w-full px-4 py-2 rounded-lg border focus:outline-none focus:border-blue-500"
                            placeholder="Search tools...">
                    </div>
                </div>
            </div>
        </section>

        <!-- Decision Tools Section -->
        <section id="decision-tools" class="py-12 bg-gray-50">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold mb-8 text-gray-800">Decision Tools</h2>
                <div id="decision-tools-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Decision tools will be dynamically inserted here -->
                </div>
            </div>
        </section>

        <!-- Random Generators Section -->
        <section id="generator-tools" class="py-12">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold mb-8 text-gray-800">Random Generators</h2>
                <div id="generator-tools-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Generator tools will be dynamically inserted here -->
                </div>
            </div>
        </section>
    </main>

    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>
    <script src="/assets/js/main.js"></script>
    <script src="/assets/js/addStructuredData.js"></script>
</body>
</html>
