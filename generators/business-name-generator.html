<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Name Generator | Company Name Ideas | RandomlyGenerate</title>
    <meta name="description" content="Generate creative and unique business names based on your description. Perfect for startups, companies, and brands. Free business name generator tool.">
    <meta name="keywords" content="business name generator, company name generator, startup name ideas, brand name generator">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://randomlygenerate.com/pages/business-name-generator.html">
    <meta property="og:title" content="Business Name Generator | Create Company Names Instantly">
    <meta property="og:description" content="Generate creative and unique business names based on your description. Perfect for startups and brands.">
    <meta property="og:image" content="https://randomlygenerate.com/assets/images/og-image.jpg">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://randomlygenerate.com/pages/business-name-generator.html">
    <meta name="twitter:title" content="Business Name Generator | Create Company Names Instantly">
    <meta name="twitter:description" content="Generate creative and unique business names based on your description. Perfect for startups and brands.">
    <meta name="twitter:image" content="https://randomlygenerate.com/assets/images/twitter-image.jpg">

    <link rel="canonical" href="https://randomlygenerate.com/pages/business-name-generator.html">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
</head>
<body class="flex flex-col min-h-screen">
    <header-component></header-component>

    <main class="flex-grow container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-4xl font-bold mb-8 text-center">Business Name Generator</h1>

            <!-- Generator Card -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <!-- Business Description -->
                <div class="mb-6">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Describe Your Business</label>
                    <textarea 
                        id="description" 
                        rows="3" 
                        class="w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter a description of your business..."
                    ></textarea>
                </div>

                <!-- Generate Button -->
                <button 
                    onclick="generateNames()" 
                    class="w-full bg-gradient-to-r from-blue-500 to-teal-500 text-white py-3 px-6 rounded-lg font-semibold hover:from-blue-600 hover:to-teal-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 mb-8"
                >
                    Generate Names
                </button>

                <!-- Results -->
                <div id="results" class="mt-6 space-y-2">
                    <!-- Results will be added here -->
                </div>
            </div>


            <!-- New SEO Content Sections -->
            <div class="max-w-4xl mx-auto mt-16 bg-[#e8f4f8] rounded-lg p-8">
                <!-- About Section -->
                <section class="mb-12">
                    <h2 class="text-2xl font-bold mb-6 text-gray-800">About Business Name Generation</h2>
                    <p class="text-gray-600 leading-relaxed">
                        Our Business Name Generator is an AI-powered tool designed to create unique and memorable company names based on your business description. Whether you're launching a startup, rebranding your company, or exploring potential business ideas, this generator combines creativity with relevance to produce names that resonate with your brand identity. Using advanced natural language processing, it analyzes your business description to generate names that reflect your industry, values, and target audience.
                    </p>
                </section>

                <!-- History Section -->
                <section class="mb-12">
                    <h2 class="text-2xl font-bold mb-6 text-gray-800">History of Business Naming</h2>
                    <p class="text-gray-600 leading-relaxed">
                        Business naming has evolved significantly from simple family names and geographical identifiers to today's sophisticated branding strategies. The industrial revolution saw the rise of corporate naming conventions, while the digital age introduced new considerations like domain availability and social media presence. Modern business naming combines traditional principles with technological innovation, using AI and machine learning to create names that are both meaningful and marketable. This evolution reflects changing business landscapes and the growing importance of digital presence in brand identity.
                    </p>
                </section>

                <!-- How to Use Section -->
                <section class="mb-12">
                    <h2 class="text-2xl font-bold mb-6 text-gray-800">How to Use Our Business Name Generator</h2>
                    <div class="text-gray-600 space-y-4">
                        <p><strong>1. Describe Your Business:</strong> Enter a detailed description of your business, including:</p>
                        <ul class="list-disc pl-6">
                            <li>Industry or sector</li>
                            <li>Target audience</li>
                            <li>Key products or services</li>
                            <li>Brand values and personality</li>
                        </ul>

                        <p><strong>2. Generate Names:</strong></p>
                        <ul class="list-disc pl-6">
                            <li>Click the "Generate Names" button</li>
                            <li>Review the list of generated names</li>
                            <li>Use the copy button to save favorites</li>
                            <li>Generate multiple times for more options</li>
                        </ul>

                        <p><strong>Best Practices:</strong></p>
                        <ul class="list-disc pl-6">
                            <li>Be specific in your business description</li>
                            <li>Consider trademark availability</li>
                            <li>Check domain name availability</li>
                            <li>Test names with potential customers</li>
                        </ul>
                    </div>
                </section>

                <!-- FAQ Section -->
                <section class="mb-12">
                    <h2 class="text-2xl font-bold mb-6 text-gray-800">Business Name Generator FAQ</h2>
                    <div class="space-y-6">
                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <h3 class="font-semibold mb-2">How does the name generator work?</h3>
                            <p class="text-gray-600">Our AI analyzes your business description and uses advanced algorithms to generate relevant, creative names that align with your brand identity.</p>
                        </div>

                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <h3 class="font-semibold mb-2">Are the generated names unique?</h3>
                            <p class="text-gray-600">While our generator creates original combinations, we recommend checking trademark databases and domain availability before finalizing your choice.</p>
                        </div>

                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <h3 class="font-semibold mb-2">Can I use these names commercially?</h3>
                            <p class="text-gray-600">Yes, but verify trademark availability and domain registration before using any name commercially.</p>
                        </div>

                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <h3 class="font-semibold mb-2">How many names can I generate?</h3>
                            <p class="text-gray-600">You can generate multiple sets of names without limitation, helping you explore various options for your business.</p>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>

    <script>
        async function generateNames() {
            const description = document.getElementById('description').value.trim();
            if (!description) {
                alert('Please describe your business');
                return;
            }

            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="text-center text-gray-600">Generating names...</div>';
            
            try {
                const prompt = `Generate 20 creative and unique business names for this description: "${description}". 
                    Names should be between 15-25 characters. Return only the names, one per line. No numbering, no quotes, no additional text.`;

                const response = await fetch('https://randomlygenerate-connectai-151124.gattr.workers.dev/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ prompt })
                });

                const data = await response.json();

                if (!data.success) {
                    throw new Error(data.error || 'Failed to generate names');
                }

                // Split the result into lines and clean up
                const names = data.result
                    .split('\n')
                    .map(name => name.trim())
                    .filter(name => 
                        name && 
                        !name.includes('```') && 
                        !name.includes('json') &&
                        !name.startsWith('[') && 
                        !name.startsWith(']') &&
                        !name.startsWith('"') &&
                        !name.endsWith('"')
                    );

                if (names.length === 0) {
                    throw new Error('No valid names were generated');
                }

                // Update UI
                updateResults(names);
                
            } catch (error) {
                console.error('Error details:', error);
                resultsDiv.innerHTML = `
                    <div class="text-center text-red-600">
                        <div>Error generating names. Please try again.</div>
                        <div class="text-sm mt-2">${error.message}</div>
                    </div>`;
            }
        }

        function updateResults(names) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = names.map(name => `
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                    <div class="font-mono">${name}</div>
                    <button 
                        onclick="copyToClipboard('', '${name.replace(/'/g, "\\'")}')"
                        class="text-blue-600 hover:text-blue-800 focus:outline-none px-3 py-1 text-sm"
                    >
                        Copy
                    </button>
                </div>
            `).join('');
        }
    </script>
</body>
</html> 