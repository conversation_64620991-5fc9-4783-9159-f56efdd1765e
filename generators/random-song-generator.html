<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Song Name Generator | Music Title Creator | RandomlyGenerate</title>
    <meta name="description" content="Generate creative song titles and music names instantly. Perfect for musicians, songwriters, and creative projects. Free song name generator.">
    <meta name="keywords" content="song name generator, music title generator, song title ideas, random song names">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://randomlygenerate.com/pages/random-song-generator.html">
    <meta property="og:title" content="Song Name Generator | Create Music Titles">
    <meta property="og:description" content="Generate creative song titles instantly. Perfect for musicians and songwriters.">
    <meta property="og:image" content="https://randomlygenerate.com/assets/images/song-og.jpg">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://randomlygenerate.com/pages/random-song-generator.html">
    <meta name="twitter:title" content="Song Name Generator | Create Music Titles">
    <meta name="twitter:description" content="Generate creative song titles instantly. Perfect for musicians and songwriters.">
    <meta name="twitter:image" content="https://randomlygenerate.com/assets/images/song-twitter.jpg">

    <link rel="canonical"
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
</head>
<body class="flex flex-col min-h-screen bg-gray-50">
    <header-component></header-component>

    <main class="flex-grow">
        <div class="max-w-4xl mx-auto p-6">
            <h1 class="text-3xl font-bold text-center mb-8">Song Name Generator</h1>

            <!-- Generator Card -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <!-- Input Fields -->
                <div class="space-y-4 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Theme or Keywords (optional)</label>
                        <input 
                            type="text" 
                            id="themeInput"
                            placeholder="Enter theme (e.g., love, summer, heartbreak)"
                            class="w-full p-3 border rounded-md"
                        >
                    </div>

                    <!-- Options -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Song Options:</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm text-gray-700 mb-1">Genre</label>
                                <select id="genreSelect" class="w-full p-2 border rounded-md">
                                    <option value="any">Any Genre</option>
                                    <option value="pop">Pop</option>
                                    <option value="rock">Rock</option>
                                    <option value="hiphop">Hip Hop</option>
                                    <option value="indie">Indie</option>
                                    <option value="electronic">Electronic</option>
                                    <option value="country">Country</option>
                                    <option value="jazz">Jazz</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm text-gray-700 mb-1">Mood</label>
                                <select id="moodSelect" class="w-full p-2 border rounded-md">
                                    <option value="any">Any Mood</option>
                                    <option value="happy">Happy</option>
                                    <option value="sad">Sad</option>
                                    <option value="energetic">Energetic</option>
                                    <option value="romantic">Romantic</option>
                                    <option value="melancholic">Melancholic</option>
                                    <option value="angry">Angry</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Generate Button -->
                <button 
                    onclick="generateSongs()"
                    class="w-full bg-gradient-to-r from-purple-400 to-pink-500 text-white py-3 px-4 rounded-md hover:opacity-90 transition-opacity font-bold text-lg"
                >
                    Generate Song Names
                </button>
            </div>

            <!-- Generated Songs -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold">Generated Song Names</h2>
                    <button 
                        onclick="clearSongs()" 
                        class="text-red-600 hover:text-red-800 text-sm"
                    >
                        Clear All
                    </button>
                </div>
                <div id="songList" class="space-y-3">
                    <!-- Songs will be added here -->
                </div>
            </div>
        </div>

        <!-- Information Section -->
        <div class="max-w-4xl mx-auto mt-16 p-6">
            <article class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">About Song Name Generation</h2>
                
                <div class="space-y-8">
                    <section>
                        <h3 class="text-xl font-semibold mb-4 text-gray-700">How It Works</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-2">
                            <li>Enter themes or keywords for inspiration</li>
                            <li>Select a specific genre and mood</li>
                            <li>Get creative song name suggestions</li>
                            <li>Copy any name with one click</li>
                        </ul>
                    </section>

                    <section>
                        <h3 class="text-xl font-semibold mb-4 text-gray-700">Name Writing Tips</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-2">
                            <li>Keep names memorable and unique</li>
                            <li>Consider your target audience</li>
                            <li>Match the name to your song's mood</li>
                            <li>Use metaphors and imagery</li>
                        </ul>
                    </section>
                </div>
            </article>
        </div>
    </main>

    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>

    <script>
        async function generateSongs() {
            const theme = document.getElementById('themeInput').value.trim();
            const genre = document.getElementById('genreSelect').value;
            const mood = document.getElementById('moodSelect').value;
            
            const songList = document.getElementById('songList');
            songList.innerHTML = '<div class="text-center text-gray-600">Generating song names...</div>';

            try {
                const prompt = `Generate 20 creative song names${theme ? ` about ${theme}` : ''}.
                    ${genre !== 'any' ? `Style: ${genre} music.` : ''}
                    ${mood !== 'any' ? `Mood: ${mood}.` : ''}
                    Make them catchy and memorable.
                    Return exactly 20 names, one per line. No numbering or additional text.
                    Each name should be unique and reflect the specified genre and mood.`;

                const response = await fetch('https://randomlygenerate-connectai-151124.gattr.workers.dev/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        prompt,
                        temperature: 0.8,
                        max_tokens: 500
                    })
                });

                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || 'Failed to generate song names');
                }

                const songs = data.result
                    .split('\n')
                    .map(song => song.trim())
                    .filter(song => song && !song.includes('```'));

                updateSongDisplay(songs);
                
            } catch (error) {
                console.error('Error:', error);
                songList.innerHTML = `
                    <div class="text-center text-red-600">
                        <div>Error generating song names. Please try again.</div>
                        <div class="text-sm mt-2">${error.message}</div>
                    </div>`;
            }
        }

        function updateSongDisplay(songs) {
            const songList = document.getElementById('songList');
            songList.className = 'grid grid-cols-1 md:grid-cols-2 gap-3';
            songList.innerHTML = songs.map(song => `
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="font-medium text-gray-800 break-words flex-1 mr-2">${song}</div>
                    <button 
                        onclick="copySong('${song.replace(/'/g, "\\'")}')"
                        class="text-blue-600 hover:text-blue-800 text-sm ml-4 whitespace-nowrap"
                    >
                        Copy
                    </button>
                </div>
            `).join('');
        }

        async function copySong(song) {
            try {
                await navigator.clipboard.writeText(song);
                const tooltip = document.createElement('div');
                tooltip.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg';
                tooltip.textContent = 'Song name copied!';
                document.body.appendChild(tooltip);
                setTimeout(() => tooltip.remove(), 2000);
            } catch (err) {
                console.error('Failed to copy:', err);
                alert('Failed to copy song name');
            }
        }

        function clearSongs() {
            if (confirm('Are you sure you want to clear all song names?')) {
                document.getElementById('songList').innerHTML = '';
                document.getElementById('themeInput').value = '';
                document.getElementById('genreSelect').value = 'any';
                document.getElementById('moodSelect').value = 'any';
            }
        }
    </script>
</body>
</html> 