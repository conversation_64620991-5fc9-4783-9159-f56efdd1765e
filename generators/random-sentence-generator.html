<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sentence Generator | Random Text Creator | RandomlyGenerate</title>
    <meta name="description" content="Generate random sentences for creative writing, examples, and content. Customize length and style. Free sentence generator tool.">
    <meta name="keywords" content="sentence generator, random sentences, text generator, writing tool, creative writing">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://randomlygenerate.com/pages/random-sentence-generator.html">
    <meta property="og:title" content="Sentence Generator | Create Random Text">
    <meta property="og:description" content="Generate random sentences for creative writing, examples, and content. Customize length and style.">
    <meta property="og:image" content="https://randomlygenerate.com/assets/images/sentence-og.jpg">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://randomlygenerate.com/pages/random-sentence-generator.html">
    <meta name="twitter:title" content="Sentence Generator | Create Random Text">
    <meta name="twitter:description" content="Generate random sentences for creative writing, examples, and content. Customize length and style.">
    <meta name="twitter:image" content="https://randomlygenerate.com/assets/images/sentence-twitter.jpg">

    <link rel="canonical" href="https://randomlygenerate.com/pages/random-sentence-generator.html">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
</head>
<body class="flex flex-col min-h-screen">
    <header-component></header-component>

    <main class="container mx-auto px-4 py-8 flex-grow">
        <div class="max-w-4xl mx-auto">
            <article class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-6">
                    <h1 class="text-3xl font-bold mb-4">Random Sentence Generator</h1>
                    <p class="text-gray-600 mb-8">Generate creative and unique sentences based on your preferred topic or style.</p>

                    <!-- Generator Card -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                        <!-- Options -->
                        <div class="space-y-4 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Topic/Style</label>
                                <input 
                                    type="text" 
                                    id="topic"
                                    class="w-full p-2 border rounded-md"
                                    placeholder="Enter topic or style (e.g., funny, inspirational, science)"
                                >
                            </div>

                            <div class="flex items-center space-x-4">
                                <div class="flex items-center">
                                    <input type="checkbox" id="complexSentences" class="mr-2" checked>
                                    <label for="complexSentences" class="text-sm text-gray-700">Complex Sentences</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="includeEmotions" class="mr-2">
                                    <label for="includeEmotions" class="text-sm text-gray-700">Include Emotions</label>
                                </div>
                            </div>
                        </div>

                        <!-- Generate Button -->
                        <button 
                            onclick="generateSentences()"
                            class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        >
                            Generate Sentences
                        </button>
                    </div>

                    <!-- Generated Sentences Section -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                        <h2 class="text-xl font-semibold mb-4">Generated Sentences</h2>
                        <div id="results" class="space-y-2">
                            <!-- Results will be added here -->
                        </div>
                    </div>

                    <!-- New SEO Content Sections -->
                    <div class="max-w-4xl mx-auto mt-16 bg-[#e8f4f8] rounded-lg p-8">
                        <!-- About Section -->
                        <section class="mb-12">
                            <h2 class="text-2xl font-bold mb-6 text-gray-800">About Sentence Generation</h2>
                            <p class="text-gray-600 leading-relaxed">
                                Sentence generation is a sophisticated blend of natural language processing and artificial intelligence that creates coherent, contextually appropriate text. This technology combines grammatical rules, semantic understanding, and linguistic patterns to produce human-like sentences. Modern sentence generators use advanced language models to understand context, maintain consistency, and create meaningful content for various purposes, from creative writing to educational materials. The ability to generate diverse, grammatically correct sentences has become increasingly important in content creation, language learning, and artificial intelligence development.
                            </p>
                        </section>

                        <!-- History Section -->
                        <section class="mb-12">
                            <h2 class="text-2xl font-bold mb-6 text-gray-800">History of Sentence Generation</h2>
                            <p class="text-gray-600 leading-relaxed">
                                The evolution of sentence generation technology traces back to early computational linguistics in the 1950s. Early systems used simple template-based approaches and basic grammar rules. The 1960s and 70s saw the development of more sophisticated context-free grammars and natural language processing techniques. The advent of machine learning in the 1990s revolutionized the field, leading to statistical models that could learn from large text corpora. Today's neural network-based language models represent a quantum leap in capability, producing increasingly natural and contextually aware text that can adapt to specific topics and styles.
                            </p>
                        </section>

                        <!-- How to Use Section -->
                        <section class="mb-12">
                            <h2 class="text-2xl font-bold mb-6 text-gray-800">How to Use Our Sentence Generator</h2>
                            <div class="text-gray-600 space-y-4">
                                <p><strong>1. Choose Your Settings:</strong></p>
                                <ul class="list-disc pl-6">
                                    <li>Enter your desired topic or theme</li>
                                    <li>Select sentence complexity level</li>
                                    <li>Toggle emotional context inclusion</li>
                                </ul>

                                <p><strong>2. Generate Content:</strong></p>
                                <ul class="list-disc pl-6">
                                    <li>Click generate for instant results</li>
                                    <li>Get multiple sentences per generation</li>
                                    <li>Review different variations</li>
                                </ul>

                                <p><strong>3. Use Your Results:</strong></p>
                                <ul class="list-disc pl-6">
                                    <li>Copy individual sentences with one click</li>
                                    <li>Use for writing prompts or examples</li>
                                    <li>Generate new sets as needed</li>
                                </ul>
                            </div>
                        </section>

                        <!-- FAQ Section -->
                        <section class="mb-12">
                            <h2 class="text-2xl font-bold mb-6 text-gray-800">Sentence Generator FAQ</h2>
                            <div class="space-y-6">
                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">How unique are the generated sentences?</h3>
                                    <p class="text-gray-600">Each sentence is uniquely generated using advanced language models, ensuring original content while maintaining natural language patterns.</p>
                                </div>

                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">Can I use these sentences in my writing?</h3>
                                    <p class="text-gray-600">Yes! The generated sentences are perfect for inspiration, examples, or as starting points for your own creative writing.</p>
                                </div>

                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">What makes a sentence "complex"?</h3>
                                    <p class="text-gray-600">Complex sentences include multiple clauses, varied punctuation, and more sophisticated grammatical structures than simple sentences.</p>
                                </div>

                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">How does emotional context work?</h3>
                                    <p class="text-gray-600">When enabled, the generator incorporates emotional language and mood indicators to create more expressive and engaging sentences.</p>
                                </div>

                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">Are the sentences grammatically correct?</h3>
                                    <p class="text-gray-600">Yes, our generator uses advanced language models to ensure proper grammar, syntax, and natural language flow.</p>
                                </div>

                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">How many sentences can I generate?</h3>
                                    <p class="text-gray-600">You can generate multiple sets of sentences, with each generation providing several unique options to choose from.</p>
                                </div>

                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">Can I specify the topic?</h3>
                                    <p class="text-gray-600">Yes, you can enter any topic or theme, and the generator will create relevant sentences within that context.</p>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </article>
        </div>
    </main>

    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>

    <script>
        async function generateSentences() {
            const topic = document.getElementById('topic').value.trim() || 'random';
            const complexSentences = document.getElementById('complexSentences').checked;
            const includeEmotions = document.getElementById('includeEmotions').checked;
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML = '<div class="text-center text-gray-600">Generating sentences...</div>';
            
            try {
                const prompt = `Generate 20 ${complexSentences ? 'complex' : 'simple'} sentences about: "${topic}". 
                    Make them creative and unique. 
                    ${includeEmotions ? 'Include emotional context or mood in the sentences.' : ''} 
                    ${complexSentences ? 'Use varied sentence structures with multiple clauses.' : 'Keep sentences concise and straightforward.'}
                    Return only the sentences, one per line. No additional text or formatting.`;

                const response = await fetch('https://randomlygenerate-connectai-151124.gattr.workers.dev/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ prompt })
                });

                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || 'Failed to generate sentences');
                }

                // Split result into lines and clean up
                const sentences = data.result
                    .split('\n')
                    .map(sentence => sentence.trim())
                    .filter(sentence => sentence && !sentence.includes('```'));

                if (sentences.length === 0) {
                    throw new Error('No valid sentences generated');
                }

                // Update UI with all sentences
                updateResults(sentences);
                
            } catch (error) {
                console.error('Error:', error);
                resultsDiv.innerHTML = `
                    <div class="text-center text-red-600">
                        <div>Error generating sentences. Please try again.</div>
                        <div class="text-sm mt-2">${error.message}</div>
                    </div>`;
            }
        }

        function updateResults(sentences) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = sentences.map(sentence => `
                <div class="flex justify-between items-start p-3 bg-gray-50 rounded-md">
                    <div class="font-mono pr-4 flex-grow">${sentence}</div>
                    <button 
                        onclick="copyToClipboard('', '${sentence.replace(/'/g, "\\'")}')"
                        class="text-blue-600 hover:text-blue-800 focus:outline-none px-3 py-1 text-sm flex-shrink-0"
                    >
                        Copy
                    </button>
                </div>
            `).join('');
        }

        function copyToClipboard(elementId = '', text = '') {
            let content = text;
            
            // Create temporary input for copying
            const tempInput = document.createElement('input');
            tempInput.value = content;
            document.body.appendChild(tempInput);
            tempInput.select();
            document.execCommand('copy');
            document.body.removeChild(tempInput);
            
            // Visual feedback
            const copyButton = event.target;
            const originalText = copyButton.textContent;
            copyButton.textContent = 'Copied!';
            setTimeout(() => {
                copyButton.textContent = originalText;
            }, 1000);
        }
    </script>
</body>
</html>
