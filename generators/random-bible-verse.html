<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bible Verse Generator | Random Scripture | RandomlyGenerate</title>
    <meta name="description" content="Generate random Bible verses by topic, book, or emotion. Multiple translations available. Perfect for daily inspiration and study.">
    <meta name="keywords" content="bible verse generator, random scripture, daily verse, bible quotes, scripture generator">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://randomlygenerate.com/pages/random-bible-verse.html">
    <meta property="og:title" content="Bible Verse Generator | Daily Scripture">
    <meta property="og:description" content="Find inspiring Bible verses randomly selected from multiple translations. Search by topic or emotion.">
    <meta property="og:image" content="https://randomlygenerate.com/assets/images/bible-verse-og.jpg">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://randomlygenerate.com/pages/random-bible-verse.html">
    <meta name="twitter:title" content="Bible Verse Generator | Daily Scripture">
    <meta name="twitter:description" content="Find inspiring Bible verses randomly selected from multiple translations. Search by topic or emotion.">
    <meta name="twitter:image" content="https://randomlygenerate.com/assets/images/bible-verse-twitter.jpg">

    <link rel="canonical" href="https://randomlygenerate.com/pages/random-bible-verse.html">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
</head>
<body class="flex flex-col min-h-screen">
    <header-component></header-component>

    <main class="container mx-auto px-4 py-8 flex-grow">
        <div class="max-w-4xl mx-auto">
            <article class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-6">
                    <h1 class="text-3xl font-bold mb-4">Random Bible Verse Generator</h1>
                    <p class="text-gray-600 mb-8">Generate inspiring Bible verses based on topics, emotions, or specific books.</p>

                    <!-- Generator Card -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                        <!-- Options -->
                        <div class="space-y-4 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Topic or Book</label>
                                <input 
                                    type="text" 
                                    id="topic"
                                    class="w-full p-2 border rounded-md"
                                    placeholder="Enter topic (e.g., love, faith, hope) or book (e.g., Psalms, John)"
                                >
                            </div>

                            <div class="flex items-center space-x-4">
                                <div class="flex items-center">
                                    <input type="checkbox" id="includeContext" class="mr-2" checked>
                                    <label for="includeContext" class="text-sm text-gray-700">Include Context</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="includeReference" class="mr-2" checked>
                                    <label for="includeReference" class="text-sm text-gray-700">Show Reference</label>
                                </div>
                            </div>
                        </div>

                        <!-- Generate Button -->
                        <button 
                            onclick="generateVerses()"
                            class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        >
                            Generate Bible Verses
                        </button>
                    </div>

                    <!-- Generated Verses Section -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                        <h2 class="text-xl font-semibold mb-4">Generated Verses</h2>
                        <div id="results" class="space-y-4">
                            <!-- Results will be added here -->
                        </div>
                    </div>

                </div>
            </article>
        </div>

        <!-- Add SEO content here, inside main -->
        <div class="max-w-4xl mx-auto mt-16 bg-[#e8f4f8] rounded-lg p-8">
            <!-- About Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">About Bible Verse Generation</h2>
                <p class="text-gray-600 leading-relaxed">
                    Bible verse generators serve as modern tools for spiritual exploration and biblical study, combining technology with sacred text to provide instant access to scripture. These tools help readers discover relevant verses for meditation, study, teaching, or seeking guidance on specific topics. Whether you're looking for verses about love, faith, hope, or wisdom, or exploring specific books of the Bible, our generator helps navigate through thousands of verses to find meaningful passages. It's particularly valuable for daily devotionals, sermon preparation, Bible study groups, or personal reflection and inspiration.
                </p>
            </section>

            <!-- History Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">History of Bible Study and Verse Selection</h2>
                <p class="text-gray-600 leading-relaxed">
                    The practice of studying and selecting Bible verses has evolved significantly throughout history. In ancient times, scripture was carefully hand-copied by scribes and studied in scrolls. The invention of the printing press in the 15th century revolutionized Bible accessibility, while the development of concordances and study guides in the following centuries made it easier to find specific passages. The digital age has transformed Bible study again, with electronic searches and verse generators making scripture more accessible than ever. From traditional lectio divina to modern digital tools, the methods of engaging with scripture continue to adapt while maintaining the sacred nature of biblical text.
                </p>
            </section>

            <!-- How to Use Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">How to Use Our Bible Verse Generator</h2>
                <div class="text-gray-600 space-y-4">
                    <p><strong>1. Choose Your Focus:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Enter a topic (e.g., love, faith, hope)</li>
                        <li>Specify a book of the Bible (e.g., Psalms, John)</li>
                        <li>Leave blank for random verses</li>
                    </ul>

                    <p><strong>2. Customize Your Results:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Toggle "Include Context" for historical background</li>
                        <li>Toggle "Show Reference" for verse locations</li>
                    </ul>

                    <p><strong>3. Generate and Explore:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Click "Generate Bible Verses"</li>
                        <li>Review the generated verses</li>
                        <li>Use the copy button to save favorites</li>
                        <li>Generate multiple times for more options</li>
                    </ul>

                    <p><strong>Example Topics:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Encouragement</li>
                        <li>Wisdom</li>
                        <li>Peace</li>
                        <li>Guidance</li>
                        <li>Healing</li>
                    </ul>
                </div>
            </section>

            <!-- FAQ Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">Bible Verse Generator FAQ</h2>
                <div class="space-y-6">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Which Bible translation is used?</h3>
                        <p class="text-gray-600">Our generator provides verses from widely-used translations, focusing on clear, accessible language while maintaining biblical accuracy.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I use this for Bible study?</h3>
                        <p class="text-gray-600">Yes! The generator is perfect for Bible study, offering contextual information and references to help deepen your understanding of scripture.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">How accurate are the verse selections for topics?</h3>
                        <p class="text-gray-600">Our system uses careful analysis to match verses with topics, ensuring relevant and meaningful connections while maintaining scriptural context.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I find verses from specific books?</h3>
                        <p class="text-gray-600">Yes, you can enter any book of the Bible to receive verses specifically from that book, or combine with a topic for more focused results.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Is the contextual information reliable?</h3>
                        <p class="text-gray-600">Yes, our contextual information is based on respected biblical scholarship and historical research, providing accurate background for better understanding.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">How often are new verses added?</h3>
                        <p class="text-gray-600">Our database includes the complete Bible, ensuring comprehensive coverage of all verses. The system is regularly updated to improve topic matching and contextual information.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I use these verses for teaching or preaching?</h3>
                        <p class="text-gray-600">Absolutely! The generator is a valuable tool for finding relevant verses for sermons, lessons, or presentations, though we recommend verifying contexts in your preferred Bible translation.</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <footer-component></footer-component>

    <!-- Scripts -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>
    <script>
        async function generateVerses() {
            const topic = document.getElementById('topic').value.trim() || 'random';
            const includeContext = document.getElementById('includeContext').checked;
            const includeReference = document.getElementById('includeReference').checked;
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML = '<div class="text-center text-gray-600">Generating verses...</div>';
            
            try {
                const prompt = `Generate 10 Bible verses ${topic !== 'random' ? `about ${topic} or from the book of ${topic}` : ''}. 
                    ${includeContext ? 'Include a brief context or explanation for each verse.' : ''} 
                    ${includeReference ? 'Include the Bible reference (book, chapter, and verse).' : ''}
                    Format each entry as: "${includeReference ? '[Reference] - ' : ''}[Verse]${includeContext ? ' - [Context]' : ''}".
                    Return only the entries, one per line. No additional text or formatting.`;

                const response = await fetch('https://randomlygenerate-connectai-151124.gattr.workers.dev/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ prompt })
                });

                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || 'Failed to generate verses');
                }

                // Split result into lines and clean up
                const verses = data.result
                    .split('\n')
                    .map(verse => verse.trim())
                    .filter(verse => verse && !verse.includes('```'));

                if (verses.length === 0) {
                    throw new Error('No valid verses generated');
                }

                // Update UI with all verses
                updateResults(verses);
                
            } catch (error) {
                console.error('Error:', error);
                resultsDiv.innerHTML = `
                    <div class="text-center text-red-600">
                        <div>Error generating verses. Please try again.</div>
                        <div class="text-sm mt-2">${error.message}</div>
                    </div>`;
            }
        }

        function updateResults(verses) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = verses.map(verse => `
                <div class="flex justify-between items-start p-4 bg-gray-50 rounded-md">
                    <div class="flex-grow pr-4">
                        <div class="text-gray-800 leading-relaxed">${verse}</div>
                    </div>
                    <button 
                        onclick="copyToClipboard('', '${verse.replace(/'/g, "\\'")}')"
                        class="text-blue-600 hover:text-blue-800 focus:outline-none px-3 py-1 text-sm flex-shrink-0"
                    >
                        Copy
                    </button>
                </div>
            `).join('');
        }

        function copyToClipboard(elementId = '', text = '') {
            let content = text;
            
            // Create temporary input for copying
            const tempInput = document.createElement('input');
            tempInput.value = content;
            document.body.appendChild(tempInput);
            tempInput.select();
            document.execCommand('copy');
            document.body.removeChild(tempInput);
            
            // Visual feedback
            const copyButton = event.target;
            const originalText = copyButton.textContent;
            copyButton.textContent = 'Copied!';
            setTimeout(() => {
                copyButton.textContent = originalText;
            }, 1000);
        }
    </script>
</body>
</html> 