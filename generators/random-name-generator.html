<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Random Name Generator | Generate Names Instantly | RandomlyGenerate</title>
    <meta name="description" content="Generate random names for characters, babies, or usernames. Choose gender, origin, and style. Free name generator with thousands of possibilities.">
    <meta name="keywords" content="name generator, random names, character names, baby names, username generator">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
</head>
<body class="flex flex-col min-h-screen">
    <header-component></header-component>

    <main class="flex-grow container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-4xl font-bold mb-8 text-center">Random Name Generator</h1>

            <!-- Generator Card -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <!-- Controls -->
                <div class="mb-8">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Name Type</label>
                    <div class="flex justify-center gap-4">
                        <button onclick="setNameType('masculine')" 
                                id="masculineBtn"
                                class="px-4 py-2 rounded-lg border-2 border-blue-500 hover:bg-blue-50 transition-colors">
                            Masculine
                        </button>
                        <button onclick="setNameType('feminine')" 
                                id="feminineBtn"
                                class="px-4 py-2 rounded-lg border-2 border-pink-500 hover:bg-pink-50 transition-colors">
                            Feminine
                        </button>
                        <button onclick="setNameType('either')" 
                                id="eitherBtn"
                                class="px-4 py-2 rounded-lg border-2 border-purple-500 hover:bg-purple-50 transition-colors">
                            Either
                        </button>
                    </div>
                </div>

                <!-- Generate Button -->
                <button onclick="generateName()" 
                        class="w-full bg-gradient-to-r from-violet-500 to-purple-600 text-white font-bold py-3 px-6 rounded-lg text-lg hover:opacity-90 transition-opacity mb-8">
                    Generate Names
                </button>

                <!-- Results -->
                <div id="results" class="mt-6 space-y-2">
                    <!-- Results will be added here -->
                </div>
            </div>

            <!-- New SEO Content Sections -->
            <div class="max-w-4xl mx-auto mt-16 bg-[#e8f4f8] rounded-lg p-8">
                <!-- About Section -->
                <section class="mb-12">
                    <h2 class="text-2xl font-bold mb-6 text-gray-800">About Name Generation</h2>
                    <p class="text-gray-600 leading-relaxed">
                        Name generation is a fascinating blend of linguistics, cultural traditions, and modern technology. Whether for naming children, creating characters for stories, or establishing online identities, names carry deep cultural significance and personal meaning. Modern name generators use sophisticated algorithms to combine phonetic patterns, cultural elements, and naming conventions to create authentic-sounding names. This technology helps writers, parents, game developers, and others find meaningful names that resonate with their intended purpose while respecting linguistic patterns and cultural contexts.
                    </p>
                </section>

                <!-- History Section -->
                <section class="mb-12">
                    <h2 class="text-2xl font-bold mb-6 text-gray-800">History of Name Creation</h2>
                    <p class="text-gray-600 leading-relaxed">
                        The practice of naming has evolved significantly throughout human history, from ancient naming ceremonies to modern digital name generation. Traditional naming methods often involved family heritage, religious significance, or natural phenomena. The 20th century saw the rise of baby name books and popularity rankings, while the digital age introduced algorithmic name generation. This technology has transformed how we approach naming in creative writing, gaming, and personal identification, making it possible to generate countless authentic-sounding names while maintaining cultural sensitivity and linguistic accuracy.
                    </p>
                </section>

                <!-- How to Use Section -->
                <section class="mb-12">
                    <h2 class="text-2xl font-bold mb-6 text-gray-800">How to Use Our Name Generator</h2>
                    <div class="text-gray-600 space-y-4">
                        <p><strong>1. Choose Name Type:</strong></p>
                        <ul class="list-disc pl-6">
                            <li>Select masculine, feminine, or gender-neutral names</li>
                            <li>Generate full names (first and last)</li>
                            <li>Get multiple options in one click</li>
                        </ul>

                        <p><strong>2. Review Options:</strong></p>
                        <ul class="list-disc pl-6">
                            <li>Browse through generated names</li>
                            <li>Check pronunciation and spelling</li>
                            <li>Consider cultural implications</li>
                        </ul>

                        <p><strong>3. Save and Share:</strong></p>
                        <ul class="list-disc pl-6">
                            <li>Copy names with one click</li>
                            <li>Generate new sets instantly</li>
                            <li>Create lists of favorites</li>
                        </ul>
                    </div>
                </section>

                <!-- FAQ Section -->
                <section class="mb-12">
                    <h2 class="text-2xl font-bold mb-6 text-gray-800">Name Generator FAQ</h2>
                    <div class="space-y-6">
                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <h3 class="font-semibold mb-2">Are these names culturally appropriate?</h3>
                            <p class="text-gray-600">Our generator creates names based on established naming conventions and cultural patterns, ensuring respectful and authentic results.</p>
                        </div>

                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <h3 class="font-semibold mb-2">Can I use these names for characters?</h3>
                            <p class="text-gray-600">Yes! These names are perfect for creative writing, role-playing games, or any character development needs.</p>
                        </div>

                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <h3 class="font-semibold mb-2">How unique are the generated names?</h3>
                            <p class="text-gray-600">Our algorithm creates diverse combinations while maintaining realistic patterns, offering unique yet believable names.</p>
                        </div>

                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <h3 class="font-semibold mb-2">Are the names suitable for real people?</h3>
                            <p class="text-gray-600">While primarily designed for creative purposes, our names follow natural naming conventions and could be suitable for personal use.</p>
                        </div>

                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <h3 class="font-semibold mb-2">How many names can I generate?</h3>
                            <p class="text-gray-600">You can generate up to 20 names per request, and there's no limit to how many times you can generate new sets.</p>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>

    <script>
        let currentType = 'either';

        async function generateName() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="text-center text-gray-600">Generating names...</div>';
            
            try {
                let prompt = `Generate 20 random`;
                if (currentType === 'masculine') {
                    prompt += " masculine";
                } else if (currentType === 'feminine') {
                    prompt += " feminine";
                }
                prompt += ` English full names (first and last name). Each name should be two words. 
                    Return only the names, one per line. No numbering or additional text. 
                    Example format:
                    Emma Thompson
                    Oliver Wright
                    Sophia Anderson`;

                const response = await fetch('https://randomlygenerate-connectai-151124.gattr.workers.dev/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ prompt })
                });

                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || 'Failed to generate names');
                }

                // Split the result into lines and clean up
                const names = data.result
                    .split('\n')
                    .map(name => name.trim())
                    .filter(name => 
                        name && 
                        !name.includes('```') && 
                        !name.includes('json') &&
                        name.split(' ').length >= 2 // Ensure each name has at least two parts
                    );

                if (names.length === 0) {
                    throw new Error('No valid names were generated');
                }

                // Update UI
                updateResults(names);
                
            } catch (error) {
                console.error('Error:', error);
                resultsDiv.innerHTML = `
                    <div class="text-center text-red-600">
                        <div>Error generating names. Please try again.</div>
                        <div class="text-sm mt-2">${error.message}</div>
                    </div>`;
            }
        }

        function updateResults(names) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = names.map(name => `
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                    <div class="font-mono">${name}</div>
                    <button 
                        onclick="copyToClipboard('', '${name.replace(/'/g, "\\'")}')"
                        class="text-blue-600 hover:text-blue-800 focus:outline-none px-3 py-1 text-sm"
                    >
                        Copy
                    </button>
                </div>
            `).join('');
        }

        function setNameType(type) {
            currentType = type;
            
            // Update button styles
            document.querySelectorAll('button').forEach(btn => {
                btn.classList.remove('bg-blue-500', 'bg-pink-500', 'bg-purple-500', 'text-white');
            });
            
            const btn = document.getElementById(`${type}Btn`);
            if (type === 'masculine') {
                btn.classList.add('bg-blue-500', 'text-white');
            } else if (type === 'feminine') {
                btn.classList.add('bg-pink-500', 'text-white');
            } else {
                btn.classList.add('bg-purple-500', 'text-white');
            }
        }

        function copyToClipboard(elementId = '', text = '') {
            let content;
            if (elementId) {
                const element = document.getElementById(elementId);
                element.select();
                content = element.value;
            } else {
                content = text;
                // Create temporary input for copying
                const tempInput = document.createElement('input');
                tempInput.value = content;
                document.body.appendChild(tempInput);
                tempInput.select();
                document.execCommand('copy');
                document.body.removeChild(tempInput);
                return;
            }
            
            document.execCommand('copy');
            
            // Visual feedback
            const copyButton = event.target;
            const originalText = copyButton.textContent;
            copyButton.textContent = 'Copied!';
            setTimeout(() => {
                copyButton.textContent = originalText;
            }, 1000);
        }

        // Initialize
        setNameType('either');
    </script>
</body>
</html> 