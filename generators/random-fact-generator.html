<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Random Fact Generator | Interesting Facts | RandomlyGenerate</title>
    <meta name="description" content="Discover fascinating random facts about science, history, nature, and more. Learn something new every day with our fact generator.">
    <meta name="keywords" content="random facts, fact generator, interesting facts, did you know facts, fun facts">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://randomlygenerate.com/pages/random-fact-generator.html">
    <meta property="og:title" content="Random Fact Generator | Learn Something New">
    <meta property="og:description" content="Discover fascinating random facts about science, history, nature, and more. Learn something new every day.">
    <meta property="og:image" content="https://randomlygenerate.com/assets/images/fact-og.jpg">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://randomlygenerate.com/pages/random-fact-generator.html">
    <meta name="twitter:title" content="Random Fact Generator | Learn Something New">
    <meta name="twitter:description" content="Discover fascinating random facts about science, history, nature, and more. Learn something new every day.">
    <meta name="twitter:image" content="https://randomlygenerate.com/assets/images/fact-twitter.jpg">

    <link rel="canonical" href="https://randomlygenerate.com/pages/random-fact-generator.html">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
</head>
<body class="flex flex-col min-h-screen bg-gray-50">
    <header-component></header-component>

    <main class="flex-grow">
        <div class="max-w-4xl mx-auto p-6">
            <h1 class="text-3xl font-bold text-center mb-8">Random Fact Generator</h1>

            <!-- Generator Card -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <!-- Input Fields -->
                <div class="space-y-4 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Topic (optional)</label>
                        <input 
                            type="text" 
                            id="topicInput"
                            placeholder="Enter topic (e.g., science, history, animals)"
                            class="w-full p-3 border rounded-md"
                        >
                    </div>

                    <!-- Options -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Options:</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <label class="flex items-center">
                                <input type="checkbox" id="includeSource" checked class="mr-2">
                                Include Source
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="includeYear" checked class="mr-2">
                                Include Year (if applicable)
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Generate Button -->
                <button 
                    onclick="generateFacts()"
                    class="w-full bg-gradient-to-r from-blue-400 to-cyan-500 text-white py-3 px-4 rounded-md hover:opacity-90 transition-opacity font-bold text-lg"
                >
                    Generate Facts
                </button>
            </div>

            <!-- Generated Facts -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold">Generated Facts</h2>
                    <button 
                        onclick="clearFacts()" 
                        class="text-red-600 hover:text-red-800 text-sm"
                    >
                        Clear All
                    </button>
                </div>
                <div id="factList" class="space-y-4">
                    <!-- Facts will be added here -->
                </div>
            </div>
        </div>

        <!-- New SEO Content Sections -->
        <div class="max-w-4xl mx-auto mt-16 bg-[#e8f4f8] rounded-lg p-8">
            <!-- About Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">About Fact Generation</h2>
                <p class="text-gray-600 leading-relaxed">
                    Random fact generation combines educational content with the excitement of discovery, offering users bite-sized pieces of knowledge from various fields of study. This modern approach to learning draws from vast databases of verified information, covering topics from science and history to culture and technology. Each generated fact undergoes verification processes to ensure accuracy, making it a reliable source for students, educators, trivia enthusiasts, and curious minds. The digital age has transformed how we consume information, and random fact generators serve as engaging tools for continuous learning and intellectual stimulation.
                </p>
            </section>

            <!-- History Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">History of Fact Sharing</h2>
                <p class="text-gray-600 leading-relaxed">
                    The tradition of sharing interesting facts dates back to ancient almanacs and encyclopedias, evolving through various forms of media over centuries. From medieval manuscripts to 19th-century penny papers featuring "Did You Know?" sections, fact-sharing has been a constant in human knowledge transfer. The digital revolution of the late 20th century introduced new ways to distribute information, leading to the development of fact databases and generators. Modern AI and machine learning technologies have further enhanced our ability to curate, verify, and present facts in engaging ways, making knowledge more accessible than ever before.
                </p>
            </section>

            <!-- How to Use Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">How to Use Our Fact Generator</h2>
                <div class="text-gray-600 space-y-4">
                    <p><strong>1. Choose Your Topic:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Enter a specific topic or leave blank for random facts</li>
                        <li>Use broad categories (science, history, nature) or specific subjects</li>
                        <li>Combine topics for more focused results</li>
                    </ul>

                    <p><strong>2. Customize Options:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Toggle source inclusion for reference information</li>
                        <li>Include years for historical context</li>
                        <li>Generate multiple facts at once</li>
                    </ul>

                    <p><strong>3. Explore and Share:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Copy individual facts with one click</li>
                        <li>Generate new sets of facts instantly</li>
                        <li>Clear and start fresh anytime</li>
                    </ul>
                </div>
            </section>

            <!-- FAQ Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">Fact Generator FAQ</h2>
                <div class="space-y-6">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">How accurate are the generated facts?</h3>
                        <p class="text-gray-600">All facts are sourced from verified databases and undergo regular fact-checking to ensure accuracy and reliability.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I use these facts for educational purposes?</h3>
                        <p class="text-gray-600">Yes! Our facts are perfect for educational use, from classroom activities to self-study. Sources are provided for further research.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">How often is the fact database updated?</h3>
                        <p class="text-gray-600">Our database is regularly updated with new facts and verified information to ensure content stays fresh and accurate.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Are the facts age-appropriate?</h3>
                        <p class="text-gray-600">Our facts are curated to be suitable for general audiences while maintaining educational value and interest.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Why do I get different facts each time?</h3>
                        <p class="text-gray-600">The generator randomly selects facts from our database to provide fresh content and diverse learning opportunities with each use.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I suggest new facts or topics?</h3>
                        <p class="text-gray-600">While we currently don't accept direct submissions, we regularly expand our database based on user interests and trending topics.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">How many facts can I generate at once?</h3>
                        <p class="text-gray-600">You can generate up to 10 unique facts per request, and there's no limit to how many times you can generate new sets.</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>

    <script>
        async function generateFacts() {
            const topic = document.getElementById('topicInput').value.trim();
            const includeSource = document.getElementById('includeSource').checked;
            const includeYear = document.getElementById('includeYear').checked;
            
            const factList = document.getElementById('factList');
            factList.innerHTML = '<div class="text-center text-gray-600">Generating facts...</div>';

            try {
                const prompt = `Generate 10 interesting${topic ? ` ${topic}-related` : ' random'} facts. 
                    Make them accurate and engaging.
                    ${includeSource ? 'Include a reliable source for each fact.' : ''} 
                    ${includeYear ? 'Include the year when relevant.' : ''}
                    Format: Fact${includeYear ? ' (Year)' : ''}${includeSource ? ' [Source]' : ''}
                    Return only the facts, one per line. No numbering or additional text.`;

                const response = await fetch('https://randomlygenerate-connectai-151124.gattr.workers.dev/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        prompt,
                        temperature: 0.7,
                        max_tokens: 1000
                    })
                });

                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || 'Failed to generate facts');
                }

                const facts = data.result
                    .split('\n')
                    .map(fact => fact.trim())
                    .filter(fact => fact && !fact.includes('```'));

                updateFactDisplay(facts);
                
            } catch (error) {
                console.error('Error:', error);
                factList.innerHTML = `
                    <div class="text-center text-red-600">
                        <div>Error generating facts. Please try again.</div>
                        <div class="text-sm mt-2">${error.message}</div>
                    </div>`;
            }
        }

        function updateFactDisplay(facts) {
            const factList = document.getElementById('factList');
            factList.innerHTML = facts.map(fact => `
                <div class="p-4 bg-gray-50 rounded-lg">
                    <div class="flex justify-between items-start gap-4">
                        <p class="text-gray-800">${fact}</p>
                        <button 
                            onclick="copyFact('${fact.replace(/'/g, "\\'")}')"
                            class="text-blue-600 hover:text-blue-800 text-sm whitespace-nowrap"
                        >
                            Copy
                        </button>
                    </div>
                </div>
            `).join('');
        }

        async function copyFact(fact) {
            try {
                await navigator.clipboard.writeText(fact);
                const tooltip = document.createElement('div');
                tooltip.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg';
                tooltip.textContent = 'Fact copied!';
                document.body.appendChild(tooltip);
                setTimeout(() => tooltip.remove(), 2000);
            } catch (err) {
                console.error('Failed to copy:', err);
                alert('Failed to copy fact');
            }
        }

        function clearFacts() {
            if (confirm('Are you sure you want to clear all facts?')) {
                document.getElementById('factList').innerHTML = '';
                document.getElementById('topicInput').value = '';
            }
        }
    </script>
</body>
</html> 