<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Motivational Quote Generator | Inspiring Quotes | RandomlyGenerate</title>
    <meta name="description" content="Generate inspiring motivational quotes instantly. Perfect for speeches, social media, and personal motivation. Free quote generator tool.">
    <meta name="keywords" content="motivational quotes, quote generator, inspiring quotes, random quotes, motivation generator">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://randomlygenerate.com/pages/random-motivational-quote.html">
    <meta property="og:title" content="Motivational Quote Generator | Daily Inspiration">
    <meta property="og:description" content="Generate inspiring motivational quotes instantly. Perfect for speeches, social media, and personal motivation.">
    <meta property="og:image" content="https://randomlygenerate.com/assets/images/quote-og.jpg">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://randomlygenerate.com/pages/random-motivational-quote.html">
    <meta name="twitter:title" content="Motivational Quote Generator | Daily Inspiration">
    <meta name="twitter:description" content="Generate inspiring motivational quotes instantly. Perfect for speeches, social media, and personal motivation.">
    <meta name="twitter:image" content="https://randomlygenerate.com/assets/images/quote-twitter.jpg">

    <link rel="canonical" href="https://randomlygenerate.com/pages/random-motivational-quote.html">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
</head>
<body class="flex flex-col min-h-screen">
    <header-component></header-component>

    <main class="container mx-auto px-4 py-8 flex-grow">
        <div class="max-w-4xl mx-auto">
            <article class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-6">
                    <h1 class="text-3xl font-bold mb-4">Random Motivational Quote Generator</h1>
                    <p class="text-gray-600 mb-8">Generate inspiring and motivational quotes based on your preferred theme or situation.</p>

                    <!-- Generator Card -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                        <!-- Options -->
                        <div class="space-y-4 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Theme/Topic</label>
                                <input 
                                    type="text" 
                                    id="theme"
                                    class="w-full p-2 border rounded-md"
                                    placeholder="Enter theme (e.g., success, perseverance, leadership)"
                                >
                            </div>

                            <div class="flex items-center space-x-4">
                                <div class="flex items-center">
                                    <input type="checkbox" id="includeAuthor" class="mr-2" checked>
                                    <label for="includeAuthor" class="text-sm text-gray-700">Include Author</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="includeContext" class="mr-2">
                                    <label for="includeContext" class="text-sm text-gray-700">Add Context</label>
                                </div>
                            </div>
                        </div>

                        <!-- Generate Button -->
                        <button 
                            onclick="generateQuotes()"
                            class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        >
                            Generate Quotes
                        </button>
                    </div>

                    <!-- Generated Quotes Section -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                        <h2 class="text-xl font-semibold mb-4">Generated Quotes</h2>
                        <div id="results" class="space-y-4">
                            <!-- Results will be added here -->
                        </div>
                    </div>

                    <!-- New SEO Content Sections -->
                    <div class="max-w-4xl mx-auto mt-16 bg-[#e8f4f8] rounded-lg p-8">
                        <!-- About Section -->
                        <section class="mb-12">
                            <h2 class="text-2xl font-bold mb-6 text-gray-800">About Motivational Quotes</h2>
                            <p class="text-gray-600 leading-relaxed">
                                Motivational quotes serve as powerful catalysts for personal growth, inspiration, and positive change. These concise pieces of wisdom, drawn from diverse sources including leaders, philosophers, artists, and visionaries, have the unique ability to shift perspectives and ignite motivation. In today's fast-paced world, motivational quotes provide quick yet meaningful doses of encouragement, helping individuals overcome challenges, pursue goals, and maintain a positive mindset. Whether used in speeches, social media, personal development, or professional settings, these quotes continue to shape human behavior and inspire action.
                            </p>
                        </section>

                        <!-- History Section -->
                        <section class="mb-12">
                            <h2 class="text-2xl font-bold mb-6 text-gray-800">History of Motivational Quotes</h2>
                            <p class="text-gray-600 leading-relaxed">
                                The tradition of sharing wisdom through concise statements dates back to ancient civilizations, from Egyptian hieroglyphs to Greek philosophical aphorisms. Throughout history, influential figures have distilled complex ideas into memorable quotes that transcend time and culture. The modern motivational quote movement gained momentum in the early 20th century with the rise of self-help literature and public speaking. The digital age has transformed how we consume and share these nuggets of wisdom, making motivational quotes more accessible and impactful than ever, while social media has created new platforms for their rapid dissemination and cultural influence.
                            </p>
                        </section>

                        <!-- How to Use Section -->
                        <section class="mb-12">
                            <h2 class="text-2xl font-bold mb-6 text-gray-800">How to Use Our Quote Generator</h2>
                            <div class="text-gray-600 space-y-4">
                                <p><strong>1. Choose Your Theme:</strong></p>
                                <ul class="list-disc pl-6">
                                    <li>Select specific themes like success, leadership, or creativity</li>
                                    <li>Leave blank for general motivational quotes</li>
                                    <li>Combine themes for more targeted inspiration</li>
                                </ul>

                                <p><strong>2. Customize Options:</strong></p>
                                <ul class="list-disc pl-6">
                                    <li>Toggle author attribution for source information</li>
                                    <li>Add context for deeper understanding</li>
                                    <li>Generate multiple quotes simultaneously</li>
                                </ul>

                                <p><strong>3. Share and Save:</strong></p>
                                <ul class="list-disc pl-6">
                                    <li>Copy quotes with one click</li>
                                    <li>Generate new sets instantly</li>
                                    <li>Use for presentations, social media, or personal motivation</li>
                                </ul>
                            </div>
                        </section>

                        <!-- FAQ Section -->
                        <section class="mb-12">
                            <h2 class="text-2xl font-bold mb-6 text-gray-800">Quote Generator FAQ</h2>
                            <div class="space-y-6">
                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">Are these quotes verified?</h3>
                                    <p class="text-gray-600">Yes, our database contains verified quotes from reliable sources, ensuring accuracy and authenticity in your inspirational content.</p>
                                </div>

                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">Can I use these quotes professionally?</h3>
                                    <p class="text-gray-600">Absolutely! These quotes are perfect for professional presentations, speeches, social media content, and business communications.</p>
                                </div>

                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">How often is the quote database updated?</h3>
                                    <p class="text-gray-600">Our database is regularly updated with new quotes from contemporary sources while maintaining a curated collection of timeless wisdom.</p>
                                </div>

                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">Why do I get different quotes each time?</h3>
                                    <p class="text-gray-600">The generator randomly selects from our extensive database to provide fresh inspiration and avoid repetition, ensuring variety in your motivational content.</p>
                                </div>

                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">Are the quotes appropriate for all audiences?</h3>
                                    <p class="text-gray-600">Our quotes are carefully curated to be suitable for general audiences while maintaining their inspirational impact.</p>
                                </div>

                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">How many quotes can I generate at once?</h3>
                                    <p class="text-gray-600">You can generate multiple quotes per request, and there's no limit to how many times you can generate new sets of inspiration.</p>
                                </div>

                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">Can I suggest new quotes or authors?</h3>
                                    <p class="text-gray-600">While we currently don't accept direct submissions, we regularly expand our database based on user feedback and trending topics.</p>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </article>
        </div>
    </main>

    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script> 

    <script>
        async function generateQuotes() {
            const theme = document.getElementById('theme').value.trim() || 'random';
            const includeAuthor = document.getElementById('includeAuthor').checked;
            const includeContext = document.getElementById('includeContext').checked;
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML = '<div class="text-center text-gray-600">Generating quotes...</div>';
            
            try {
                const prompt = `Generate 15 inspiring motivational quotes${theme !== 'random' ? ` about ${theme}` : ''}. 
                    Make them powerful and memorable. 
                    ${includeAuthor ? 'Include a real or fictional author for each quote.' : ''} 
                    ${includeContext ? 'Add a brief context or explanation after each quote.' : ''}
                    Format each entry as: "Quote${includeAuthor ? ' - Author' : ''}${includeContext ? ' | Context' : ''}"
                    Return only the quotes, one per line. No additional text or formatting.`;

                const response = await fetch('https://randomlygenerate-connectai-151124.gattr.workers.dev/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ prompt })
                });

                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || 'Failed to generate quotes');
                }

                // Split result into lines and clean up
                const quotes = data.result
                    .split('\n')
                    .map(quote => quote.trim())
                    .filter(quote => quote && !quote.includes('```'));

                if (quotes.length === 0) {
                    throw new Error('No valid quotes generated');
                }

                // Update UI with all quotes
                updateResults(quotes);
                
            } catch (error) {
                console.error('Error:', error);
                resultsDiv.innerHTML = `
                    <div class="text-center text-red-600">
                        <div>Error generating quotes. Please try again.</div>
                        <div class="text-sm mt-2">${error.message}</div>
                    </div>`;
            }
        }

        function updateResults(quotes) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = quotes.map(quote => `
                <div class="flex justify-between items-start p-4 bg-gray-50 rounded-md">
                    <div class="flex-grow pr-4">
                        <div class="text-gray-800 leading-relaxed italic">${quote}</div>
                    </div>
                    <button 
                        onclick="copyToClipboard('', '${quote.replace(/'/g, "\\'")}')"
                        class="text-blue-600 hover:text-blue-800 focus:outline-none px-3 py-1 text-sm flex-shrink-0"
                    >
                        Copy
                    </button>
                </div>
            `).join('');
        }

        function copyToClipboard(elementId = '', text = '') {
            let content = text;
            
            // Create temporary input for copying
            const tempInput = document.createElement('input');
            tempInput.value = content;
            document.body.appendChild(tempInput);
            tempInput.select();
            document.execCommand('copy');
            document.body.removeChild(tempInput);
            
            // Visual feedback
            const copyButton = event.target;
            const originalText = copyButton.textContent;
            copyButton.textContent = 'Copied!';
            setTimeout(() => {
                copyButton.textContent = originalText;
            }, 1000);
        }
    </script>
</body>
</html> 