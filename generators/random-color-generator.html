<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Generator | Color Palette Creator | RandomlyGenerate</title>
    <meta name="description" content="Generate beautiful color palettes and hex codes instantly. Perfect for web design, branding, and creative projects. Free color generator tool.">
    <meta name="keywords" content="color generator, color palette generator, random colors, hex code generator, color schemes">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://randomlygenerate.com/pages/random-color-generator.html">
    <meta property="og:title" content="Color Generator | Create Beautiful Color Palettes">
    <meta property="og:description" content="Generate beautiful color palettes and hex codes instantly. Perfect for web design and creative projects.">
    <meta property="og:image" content="https://randomlygenerate.com/assets/images/color-og.jpg">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://randomlygenerate.com/pages/random-color-generator.html">
    <meta name="twitter:title" content="Color Generator | Create Beautiful Color Palettes">
    <meta name="twitter:description" content="Generate beautiful color palettes and hex codes instantly. Perfect for web design and creative projects.">
    <meta name="twitter:image" content="https://randomlygenerate.com/assets/images/color-twitter.jpg">

    <link rel="canonical" href="https://randomlygenerate.com/pages/random-color-generator.html">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
</head>
<body class="flex flex-col min-h-screen bg-gray-50">
    <header-component></header-component>

    <main class="flex-grow">
        <div class="max-w-4xl mx-auto p-6">
            <h1 class="text-3xl font-bold text-center mb-8">Color Generator</h1>

            <!-- Generator Card -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <!-- Input Fields -->
                <div class="space-y-4 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Enter Color Name or Description</label>
                        <input 
                            type="text" 
                            id="colorInput"
                            placeholder="Enter color (e.g., sunset orange, deep ocean blue)"
                            class="w-full p-3 border rounded-md"
                        >
                    </div>

                    <!-- Options -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Options:</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <label class="flex items-center">
                                <input type="checkbox" id="includeDarker" checked class="mr-2">
                                Include Darker Shades
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="includeLighter" checked class="mr-2">
                                Include Lighter Shades
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Generate Button -->
                <button 
                    onclick="generateColors()"
                    class="w-full bg-gradient-to-r from-pink-500 to-orange-500 text-white py-3 px-4 rounded-md hover:opacity-90 transition-opacity font-bold text-lg"
                >
                    Generate Colors
                </button>
            </div>

            <!-- Generated Colors -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold">Generated Color Palette</h2>
                    <button 
                        onclick="clearColors()" 
                        class="text-red-600 hover:text-red-800 text-sm"
                    >
                        Clear All
                    </button>
                </div>
                <div id="colorList" class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <!-- Colors will be added here -->
                </div>
            </div>
        </div>

        <!-- New SEO Content Sections -->
        <div class="max-w-4xl mx-auto mt-16 bg-[#e8f4f8] rounded-lg p-8">
            <!-- About Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">About Color Generation</h2>
                <p class="text-gray-600 leading-relaxed">
                    Color generation is a fundamental aspect of digital design and creative work. Our color generator combines artificial intelligence with color theory to create harmonious color combinations from natural language descriptions. Whether you're designing websites, creating artwork, or developing brand identities, having the right color palette is crucial. This tool bridges the gap between human perception of color and technical color codes, making it easier for both beginners and professionals to find and experiment with colors that match their vision.
                </p>
            </section>

            <!-- History Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">History of Color Theory and Digital Colors</h2>
                <p class="text-gray-600 leading-relaxed">
                    The journey of color theory began with Newton's spectrum experiments in the 1660s, evolving through various color wheels and systems by artists and scientists. The digital revolution brought the RGB and HEX color systems, standardizing how we represent colors in digital formats. The introduction of hex codes in the 1970s revolutionized web design, allowing precise color specification across different platforms. Today, AI-powered color generation represents the latest evolution, enabling natural language processing to interpret color descriptions and generate matching digital color codes, making color selection more intuitive and accessible than ever before.
                </p>
            </section>

            <!-- How to Use Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">How to Use Our Color Generator</h2>
                <div class="text-gray-600 space-y-4">
                    <p><strong>1. Describe Your Color:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Enter descriptive terms like "sunset orange" or "deep ocean blue"</li>
                        <li>Use nature-inspired descriptions for more organic colors</li>
                        <li>Combine emotions with colors (e.g., "calming blue")</li>
                    </ul>

                    <p><strong>2. Customize Options:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Toggle "Include Darker Shades" for deeper variations</li>
                        <li>Toggle "Include Lighter Shades" for softer tones</li>
                    </ul>

                    <p><strong>3. Generate and Save:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Click "Generate Colors" to create your palette</li>
                        <li>Copy hex codes with one click</li>
                        <li>Generate multiple times for more options</li>
                        <li>Clear results to start fresh</li>
                    </ul>

                    <p><strong>Example Descriptions:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>"Tropical sunset"</li>
                        <li>"Forest greens"</li>
                        <li>"Desert sand"</li>
                        <li>"Arctic ice"</li>
                        <li>"Autumn leaves"</li>
                    </ul>
                </div>
            </section>

            <!-- FAQ Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">Color Generator FAQ</h2>
                <div class="space-y-6">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">How accurate are the generated colors to my description?</h3>
                        <p class="text-gray-600">Our AI system is trained to understand natural language color descriptions and convert them into accurate digital colors, though results may vary based on the specificity of your description.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I use these colors for commercial projects?</h3>
                        <p class="text-gray-600">Yes! All generated colors are free to use in any project, whether personal or commercial. The hex codes are standard web colors without any usage restrictions.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Why do I get different shades each time?</h3>
                        <p class="text-gray-600">The generator creates variations within your specified color family to provide options and inspire creativity. Each generation is unique while staying true to your description.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Are the hex codes web-safe?</h3>
                        <p class="text-gray-600">Yes, all generated hex codes are standard 6-digit web colors that work across all modern browsers and design applications.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I generate color palettes for specific themes?</h3>
                        <p class="text-gray-600">Absolutely! Try using theme-specific descriptions like "beach vacation" or "winter forest" to generate cohesive color palettes that match your theme.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">How many colors can I generate at once?</h3>
                        <p class="text-gray-600">Each generation provides 20 unique colors with their hex codes and descriptions. You can generate multiple times to explore more options.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I save my favorite colors?</h3>
                        <p class="text-gray-600">While the generator doesn't store colors permanently, you can easily copy the hex codes of your favorite colors to save them for later use.</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>

    <script>
        async function generateColors() {
            const colorInput = document.getElementById('colorInput').value.trim();
            const includeDarker = document.getElementById('includeDarker').checked;
            const includeLighter = document.getElementById('includeLighter').checked;
            
            if (!colorInput) {
                alert('Please enter a color description');
                return;
            }

            const colorList = document.getElementById('colorList');
            colorList.innerHTML = '<div class="col-span-full text-center text-gray-600">Generating colors...</div>';

            try {
                const prompt = `Generate 20 hex color codes for "${colorInput}". 
                    ${includeDarker ? 'Include darker shades.' : ''} 
                    ${includeLighter ? 'Include lighter shades.' : ''}
                    Return only the hex codes with brief color descriptions, one per line.
                    Format each line as: #HEXCODE - Description
                    Example:
                    #FF5733 - Bright coral orange
                    #FF8C69 - Salmon pink`;

                const response = await fetch('https://randomlygenerate-connectai-151124.gattr.workers.dev/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer your-api-key-here'
                    },
                    body: JSON.stringify({ 
                        prompt,
                        temperature: 0.7,
                        max_tokens: 500
                    })
                });

                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || 'Failed to generate colors');
                }

                const colors = data.result
                    .split('\n')
                    .map(line => line.trim())
                    .filter(line => line && line.includes('#'))
                    .map(line => {
                        const [hex, ...descParts] = line.split('-');
                        return {
                            hex: hex.trim(),
                            description: descParts.join('-').trim()
                        };
                    });

                updateColorDisplay(colors);
                
            } catch (error) {
                console.error('Error:', error);
                colorList.innerHTML = `
                    <div class="col-span-full text-center text-red-600">
                        <div>Error generating colors. Please try again.</div>
                        <div class="text-sm mt-2">${error.message}</div>
                    </div>`;
            }
        }

        function updateColorDisplay(colors) {
            const colorList = document.getElementById('colorList');
            colorList.innerHTML = colors.map(color => `
                <div class="flex flex-col rounded-lg overflow-hidden shadow-sm border">
                    <div 
                        class="h-24 w-full"
                        style="background-color: ${color.hex}"
                    ></div>
                    <div class="p-3 bg-white">
                        <div class="text-sm font-mono mb-1">${color.hex}</div>
                        <div class="text-xs text-gray-600 mb-2">${color.description}</div>
                        <button 
                            onclick="copyColor('${color.hex}')"
                            class="text-blue-600 hover:text-blue-800 text-xs"
                        >
                            Copy Hex
                        </button>
                    </div>
                </div>
            `).join('');
        }

        async function copyColor(hex) {
            try {
                await navigator.clipboard.writeText(hex);
                const tooltip = document.createElement('div');
                tooltip.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg';
                tooltip.textContent = 'Color code copied!';
                document.body.appendChild(tooltip);
                setTimeout(() => tooltip.remove(), 2000);
            } catch (err) {
                console.error('Failed to copy:', err);
                alert('Failed to copy color code');
            }
        }

        function clearColors() {
            if (confirm('Are you sure you want to clear all colors?')) {
                document.getElementById('colorList').innerHTML = '';
                document.getElementById('colorInput').value = '';
            }
        }
    </script>
</body>
</html> 