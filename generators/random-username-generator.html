<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Username Generator | Create Unique Usernames | RandomlyGenerate</title>
    <meta name="description" content="Generate creative and unique usernames instantly. Perfect for gaming, social media, and online accounts. Free username generator tool.">
    <meta name="keywords" content="username generator, random username, gaming username, social media names, unique usernames">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://randomlygenerate.com/pages/random-username-generator.html">
    <meta property="og:title" content="Username Generator | Create Unique Online Names">
    <meta property="og:description" content="Generate creative and unique usernames instantly. Perfect for gaming and social media accounts.">
    <meta property="og:image" content="https://randomlygenerate.com/assets/images/username-og.jpg">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://randomlygenerate.com/pages/random-username-generator.html">
    <meta name="twitter:title" content="Username Generator | Create Unique Online Names">
    <meta name="twitter:description" content="Generate creative and unique usernames instantly. Perfect for gaming and social media accounts.">
    <meta name="twitter:image" content="https://randomlygenerate.com/assets/images/username-twitter.jpg">

    <link rel="canonical" href="https://randomlygenerate.com/pages/random-username-generator.html">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
</head>
<body class="flex flex-col min-h-screen">
    <header-component></header-component>

    <main class="container mx-auto px-4 py-8 flex-grow">
        <div class="max-w-4xl mx-auto">
            <article class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-6">
                    <h1 class="text-3xl font-bold mb-4">Random Username Generator</h1>
                    <p class="text-gray-600 mb-8">Generate unique and creative usernames instantly based on your preferred theme.</p>

                    <!-- Generator Card -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                        <!-- Options -->
                        <div class="space-y-4 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Theme</label>
                                <input 
                                    type="text" 
                                    id="theme"
                                    class="w-full p-2 border rounded-md"
                                    placeholder="Enter theme (e.g., animals, vehicles, nature)"
                                >
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" id="includeNumbers" class="mr-2" checked>
                                <label for="includeNumbers" class="text-sm text-gray-700">Include Numbers</label>
                            </div>
                        </div>

                        <!-- Generate Button -->
                        <button 
                            onclick="generateUsername()"
                            class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        >
                            Generate Usernames
                        </button>
                    </div>

                    <!-- Generated Usernames Section -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                        <h2 class="text-xl font-semibold mb-4">Generated Usernames</h2>
                        <div id="results" class="space-y-2">
                            <!-- Results will be added here -->
                        </div>
                    </div>

                    <!-- New SEO Content Sections -->
                    <div class="max-w-4xl mx-auto mt-16 bg-[#e8f4f8] rounded-lg p-8">
                        <!-- About Section -->
                        <section class="mb-12">
                            <h2 class="text-2xl font-bold mb-6 text-gray-800">About Username Generation</h2>
                            <p class="text-gray-600 leading-relaxed">
                                Username generation is a vital part of establishing digital identity across various online platforms. Modern username generators combine creativity with functionality to create unique identifiers that reflect personality while maintaining anonymity. These tools have become essential in an era where digital presence spans social media, gaming, professional networks, and online communities. By utilizing advanced algorithms and customization options, username generators help users overcome the challenge of finding available, memorable, and appropriate usernames in increasingly crowded digital spaces.
                            </p>
                        </section>

                        <!-- History Section -->
                        <section class="mb-12">
                            <h2 class="text-2xl font-bold mb-6 text-gray-800">History of Online Usernames</h2>
                            <p class="text-gray-600 leading-relaxed">
                                The concept of usernames emerged in the 1960s with early computer systems requiring unique identifiers for multi-user environments. The advent of email in the 1970s popularized personal digital identifiers, while the 1990s internet boom created unprecedented demand for unique usernames. Social media's rise in the 2000s transformed usernames from simple identifiers into personal brand elements, leading to increasingly creative naming conventions. Today's username generation tools reflect this evolution, incorporating AI and sophisticated algorithms to meet the growing need for distinctive digital identities across diverse platforms.
                            </p>
                        </section>

                        <!-- How to Use Section -->
                        <section class="mb-12">
                            <h2 class="text-2xl font-bold mb-6 text-gray-800">How to Use Our Username Generator</h2>
                            <div class="text-gray-600 space-y-4">
                                <p><strong>1. Input Preferences:</strong></p>
                                <ul class="list-disc pl-6">
                                    <li>Enter keywords or themes you'd like to include</li>
                                    <li>Choose whether to include numbers</li>
                                    <li>Select special character preferences</li>
                                </ul>

                                <p><strong>2. Generate Options:</strong></p>
                                <ul class="list-disc pl-6">
                                    <li>Click generate to see multiple suggestions</li>
                                    <li>Review different variations</li>
                                    <li>Generate new sets until you find the perfect match</li>
                                </ul>

                                <p><strong>3. Use Your Username:</strong></p>
                                <ul class="list-disc pl-6">
                                    <li>Copy your chosen username with one click</li>
                                    <li>Test availability on your target platform</li>
                                    <li>Save favorites for later consideration</li>
                                </ul>
                            </div>
                        </section>

                        <!-- FAQ Section -->
                        <section class="mb-12">
                            <h2 class="text-2xl font-bold mb-6 text-gray-800">Username Generator FAQ</h2>
                            <div class="space-y-6">
                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">Are generated usernames unique?</h3>
                                    <p class="text-gray-600">While our generator creates unique combinations, we recommend checking availability on your specific platform as usernames might already be taken by others.</p>
                                </div>

                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">Can I customize the generated usernames?</h3>
                                    <p class="text-gray-600">Yes, you can influence the results by entering specific words and choosing whether to include numbers or special characters.</p>
                                </div>

                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">How many usernames can I generate?</h3>
                                    <p class="text-gray-600">You can generate unlimited username combinations and create new sets as often as needed until you find the perfect match.</p>
                                </div>

                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">Are these usernames safe to use?</h3>
                                    <p class="text-gray-600">Yes, our generator creates platform-friendly usernames that avoid offensive content and follow common username conventions.</p>
                                </div>

                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">Will these work on any platform?</h3>
                                    <p class="text-gray-600">Our generated usernames follow common platform guidelines, but specific services may have unique requirements for length or allowed characters.</p>
                                </div>

                                <div class="bg-white p-6 rounded-lg shadow-sm">
                                    <h3 class="font-semibold mb-2">Can I save my favorite usernames?</h3>
                                    <p class="text-gray-600">While the generator doesn't store usernames permanently, you can copy and save your favorites elsewhere for future reference.</p>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </article>
        </div>
    </main>

    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>

    <script>
        async function generateUsername() {
            const theme = document.getElementById('theme').value.trim() || 'random';
            const includeNumbers = document.getElementById('includeNumbers').checked;
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML = '<div class="text-center text-gray-600">Generating usernames...</div>';
            
            try {
                const prompt = `Generate 20 creative usernames based on theme: "${theme}". 
                    Make them unique and memorable. 
                    ${includeNumbers ? 'Add a random number between 1-999 at the end of each username.' : ''} 
                    Return only the usernames, one per line. No additional text or formatting.`;

                const response = await fetch('https://randomlygenerate-connectai-151124.gattr.workers.dev/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ prompt })
                });

                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || 'Failed to generate usernames');
                }

                // Split result into lines and clean up
                const usernames = data.result
                    .split('\n')
                    .map(name => name.trim())
                    .filter(name => name && !name.includes('```'));

                if (usernames.length === 0) {
                    throw new Error('No valid usernames generated');
                }

                // Update UI with all usernames
                updateResults(usernames);
                
            } catch (error) {
                console.error('Error:', error);
                resultsDiv.innerHTML = `
                    <div class="text-center text-red-600">
                        <div>Error generating usernames. Please try again.</div>
                        <div class="text-sm mt-2">${error.message}</div>
                    </div>`;
            }
        }

        function updateResults(usernames) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = usernames.map(username => `
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                    <div class="font-mono">${username}</div>
                    <button 
                        onclick="copyToClipboard('', '${username.replace(/'/g, "\\'")}')"
                        class="text-blue-600 hover:text-blue-800 focus:outline-none px-3 py-1 text-sm"
                    >
                        Copy
                    </button>
                </div>
            `).join('');
        }

        function copyToClipboard(elementId = '', text = '') {
            let content = text;
            
            // Create temporary input for copying
            const tempInput = document.createElement('input');
            tempInput.value = content;
            document.body.appendChild(tempInput);
            tempInput.select();
            document.execCommand('copy');
            document.body.removeChild(tempInput);
            
            // Visual feedback
            const copyButton = event.target;
            const originalText = copyButton.textContent;
            copyButton.textContent = 'Copied!';
            setTimeout(() => {
                copyButton.textContent = originalText;
            }, 1000);
        }
    </script>
</body>
</html> 