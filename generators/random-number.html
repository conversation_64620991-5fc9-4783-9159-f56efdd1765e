<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Random Number Generator | Number Picker | RandomlyGenerate</title>
    <meta name="description" content="Generate random numbers within any range. Perfect for games, decisions, raffles, and more. Fast, fair, and truly random number generator.">
    <meta name="keywords" content="random number generator, number picker, random integer, number randomizer">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
    <style>
        .number-display {
            font-size: 5rem;
            font-weight: bold;
            height: 200px;
            margin: 2rem auto;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .number-display.show {
            opacity: 1;
        }

        .generating {
            animation: pulse 1s ease-in-out;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        input[type="number"] {
            -moz-appearance: textfield;
        }

        input[type="number"]::-webkit-outer-spin-button,
        input[type="number"]::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
    </style>
</head>
<body class="flex flex-col min-h-screen">
    <header-component></header-component>

    <main class="flex-grow container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto text-center">
            <h1 class="text-4xl font-bold mb-8">Random Number Generator</h1>
            
            <div class="number-display text-gray-800" id="result">?</div>

            <div class="flex gap-4 justify-center mb-8">
                <div class="w-32">
                    <label for="fromNumber" class="block text-sm font-medium text-gray-700 mb-1">From</label>
                    <input type="number" 
                           id="fromNumber" 
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                           value="1">
                </div>
                <div class="w-32">
                    <label for="toNumber" class="block text-sm font-medium text-gray-700 mb-1">To</label>
                    <input type="number" 
                           id="toNumber" 
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                           value="100">
                </div>
            </div>

            <div class="mt-8">
                <button onclick="generateNumber()" 
                        class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-4 px-8 rounded-lg text-xl transition duration-300">
                    GENERATE!
                </button>
            </div>
        </div>

        <!-- New SEO Content Sections -->
        <div class="max-w-4xl mx-auto mt-16 bg-[#e8f4f8] rounded-lg p-8">
            <!-- About Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">About Random Number Generation</h2>
                <p class="text-gray-600 leading-relaxed">
                    Random number generation is a fundamental process in computing and mathematics that produces numbers through a method that makes predicting the next number impossible. From simple games to complex cryptography, random numbers play a crucial role in many applications. Modern random number generators use sophisticated algorithms and hardware-based entropy sources to ensure true randomness, making them essential tools for scientific research, gaming, cybersecurity, and statistical sampling. Our generator employs cryptographic-grade randomization to provide truly unpredictable numbers within any specified range.
                </p>
            </section>

            <!-- History Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">History of Random Numbers</h2>
                <p class="text-gray-600 leading-relaxed">
                    The quest for generating random numbers dates back to ancient civilizations using dice and lots for decision-making. The modern era of random number generation began in the 1940s with RAND Corporation's publication of a million random digits, generated through physical methods. The digital age introduced algorithmic pseudo-random number generators, while quantum mechanics and atmospheric noise provided new sources of true randomness. Today's cryptographic random number generators combine multiple entropy sources with sophisticated algorithms to produce high-quality random numbers essential for digital security, scientific simulations, and fair gaming systems.
                </p>
            </section>

            <!-- How to Use Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">How to Use Our Number Generator</h2>
                <div class="text-gray-600 space-y-4">
                    <p><strong>1. Set Your Range:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Enter your minimum number in the "From" field</li>
                        <li>Enter your maximum number in the "To" field</li>
                        <li>Use any whole numbers within JavaScript's safe integer range</li>
                    </ul>

                    <p><strong>2. Generate Numbers:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Click the "Generate" button for instant results</li>
                        <li>Watch the animation for added excitement</li>
                        <li>Get a new random number with each click</li>
                    </ul>

                    <p><strong>3. Use Your Results:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Use for games, raffles, or decision-making</li>
                        <li>Generate multiple numbers as needed</li>
                        <li>Reset range inputs for different scenarios</li>
                    </ul>
                </div>
            </section>

            <!-- FAQ Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">Random Number Generator FAQ</h2>
                <div class="space-y-6">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">How random are the generated numbers?</h3>
                        <p class="text-gray-600">Our generator uses cryptographic-grade randomization through the Web Crypto API, ensuring true randomness suitable for any application.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">What's the largest number I can generate?</h3>
                        <p class="text-gray-600">You can generate numbers up to JavaScript's maximum safe integer (9,007,199,254,740,991), making it suitable for virtually any practical use.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I use this for lucky draws and giveaways?</h3>
                        <p class="text-gray-600">Absolutely! Our generator is perfect for selecting winners in lucky draws, giveaways, team assignments, party games, or any fun event where you need fair and random selection.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Are the numbers truly random?</h3>
                        <p class="text-gray-600">Yes, we use the Web Crypto API which provides cryptographically secure random numbers, ensuring true randomness unlike traditional pseudo-random generators.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">How often can I generate numbers?</h3>
                        <p class="text-gray-600">You can generate new numbers as frequently as you need, with a brief animation between generations for visual feedback.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I get the same number twice?</h3>
                        <p class="text-gray-600">Yes, each generation is independent, so any number within your specified range has an equal chance of being selected each time.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Is this suitable for scientific applications?</h3>
                        <p class="text-gray-600">Our generator uses cryptographic-grade randomization, making it suitable for scientific sampling, simulations, and statistical applications.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">What can I use this generator for?</h3>
                        <p class="text-gray-600">Our generator is perfect for tabletop RPGs like D&D (rolling virtual d20s or custom dice), picking random winners for giveaways, choosing players for team games, selecting random items from lists, or any fun activity needing fair random selection.</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>
    
    <script>
        let isGenerating = false;

        function generateNumber() {
            if (isGenerating) return;
            
            const fromNum = parseInt(document.getElementById('fromNumber').value);
            const toNum = parseInt(document.getElementById('toNumber').value);
            
            if (isNaN(fromNum) || isNaN(toNum)) {
                alert('Please enter valid numbers');
                return;
            }
            
            if (fromNum >= toNum) {
                alert('The "From" number must be less than the "To" number');
                return;
            }
            
            isGenerating = true;
            const display = document.getElementById('result');
            display.classList.remove('show');
            display.classList.add('generating');
            
            setTimeout(() => {
                // Use a single crypto random value for true randomness
                const randomBuffer = new Uint32Array(1);
                window.crypto.getRandomValues(randomBuffer);
                
                // Simple linear transformation to map the random value to our range
                const range = toNum - fromNum + 1;
                const result = Math.floor((randomBuffer[0] / 0x100000000) * range) + fromNum;
                
                // Format the number without grouping
                const formattedResult = result.toString();
                
                display.textContent = formattedResult;
                display.classList.remove('generating');
                display.classList.add('show');
                
                // Dynamic font size adjustment based on number length
                if (formattedResult.length > 4) {
                    display.style.fontSize = '3.5rem';
                } else if (formattedResult.length > 3) {
                    display.style.fontSize = '4rem';
                } else if (formattedResult.length > 2) {
                    display.style.fontSize = '4.5rem';
                } else {
                    display.style.fontSize = '5rem';
                }
                
                isGenerating = false;
            }, 1000);
        }

        // Reset font size and display when inputs change
        document.getElementById('fromNumber').addEventListener('input', function() {
            const display = document.getElementById('result');
            display.textContent = '?';
            display.style.fontSize = '5rem';
        });
        
        document.getElementById('toNumber').addEventListener('input', function() {
            const display = document.getElementById('result');
            display.textContent = '?';
            display.style.fontSize = '5rem';
        });
    </script>
</body>
</html> 