<!DOCTYPE html>
<html lang="en"> 
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Username Generator | Create Email Names | RandomlyGenerate</title>
    <meta name="description" content="Generate unique and professional email usernames instantly. Perfect for business or personal use. Free email username generator tool.">
    <meta name="keywords" content="email username generator, email name generator, professional email names, email address creator">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://randomlygenerate.com/pages/random-email-generator.html">
    <meta property="og:title" content="Email Username Generator | Create Professional Email Names">
    <meta property="og:description" content="Generate unique and professional email usernames instantly. Perfect for business or personal use.">
    <meta property="og:image" content="https://randomlygenerate.com/assets/images/og-image.jpg">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://randomlygenerate.com/pages/random-email-generator.html">
    <meta name="twitter:title" content="Email Username Generator | Create Professional Email Names">
    <meta name="twitter:description" content="Generate unique and professional email usernames instantly. Perfect for business or personal use.">
    <meta name="twitter:image" content="https://randomlygenerate.com/assets/images/twitter-image.jpg">

    <link rel="canonical" href="https://randomlygenerate.com/pages/random-email-generator.html">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
</head>
<body class="flex flex-col min-h-screen bg-gray-50">
    <header-component></header-component>

    <main class="flex-grow">
        <div class="max-w-4xl mx-auto p-6">
            <h1 class="text-3xl font-bold text-center mb-8">Email Username Generator</h1>

            <!-- Generator Card -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <!-- Input Fields -->
                <div class="space-y-4 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Name or Keywords (optional)</label>
                        <input 
                            type="text" 
                            id="nameInput"
                            placeholder="Enter name or keywords (e.g., john smith, business)"
                            class="w-full p-3 border rounded-md"
                        >
                    </div>

                    <!-- Options -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email Options:</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <label class="flex items-center">
                                <input type="checkbox" id="includeNumbers" checked class="mr-2">
                                Add Numbers
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="includeDots" checked class="mr-2">
                                Include Dots
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="includeUnderscores" class="mr-2">
                                Include Underscores
                            </label>
                            <div>
                                <label class="block text-sm text-gray-700 mb-1">Style</label>
                                <select id="styleSelect" class="w-full p-2 border rounded-md">
                                    <option value="any">Any Style</option>
                                    <option value="professional">Professional</option>
                                    <option value="casual">Casual</option>
                                    <option value="creative">Creative</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Generate Button -->
                <button 
                    onclick="generateEmails()"
                    class="w-full bg-gradient-to-r from-teal-400 to-emerald-500 text-white py-3 px-4 rounded-md hover:opacity-90 transition-opacity font-bold text-lg"
                >
                    Generate Email Usernames
                </button>
            </div>

            <!-- Generated Emails -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold">Generated Email Usernames</h2>
                    <button 
                        onclick="clearEmails()" 
                        class="text-red-600 hover:text-red-800 text-sm"
                    >
                        Clear All
                    </button>
                </div>
                <div id="emailList" class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <!-- Emails will be added here -->
                </div>
            </div>
        </div>

        <!-- New SEO Content Sections -->
        <div class="max-w-4xl mx-auto mt-16 bg-[#e8f4f8] rounded-lg p-8">
            <!-- About Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">About Email Username Generation</h2>
                <p class="text-gray-600 leading-relaxed">
                    Email username generation is a crucial aspect of digital identity creation, combining creativity with professionalism to craft unique online identifiers. Whether for business, personal use, or specialized accounts, having a well-crafted email username helps establish your digital presence. Modern email username generators use sophisticated algorithms to create memorable, appropriate, and available usernames while considering factors like professionalism, memorability, and uniqueness. This tool helps individuals and businesses overcome the challenge of finding available usernames in an increasingly crowded digital space.
                </p>
            </section>

            <!-- History Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">History of Email Usernames</h2>
                <p class="text-gray-600 leading-relaxed">
                    The evolution of email usernames traces back to the early days of electronic mail in the 1970s, when simple usernames were sufficient due to limited user bases. As email adoption grew exponentially in the 1990s, username creation became more complex, requiring creative combinations of names, numbers, and special characters. The rise of professional email communication in the 2000s introduced new conventions focusing on business-appropriate usernames. Today's username generation considers multiple factors including personal branding, privacy concerns, and cross-platform compatibility, reflecting the sophisticated needs of modern digital communication.
                </p>
            </section>

            <!-- How to Use Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">How to Use Our Email Username Generator</h2>
                <div class="text-gray-600 space-y-4">
                    <p><strong>1. Enter Your Details:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Input your name or preferred keywords</li>
                        <li>Choose your preferred style (professional, casual, creative)</li>
                        <li>Select formatting options (numbers, dots, underscores)</li>
                    </ul>

                    <p><strong>2. Customize Options:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Toggle number inclusion for more variations</li>
                        <li>Add dots for professional formatting</li>
                        <li>Include underscores for unique combinations</li>
                    </ul>

                    <p><strong>3. Generate and Select:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Click "Generate Email Usernames"</li>
                        <li>Review the generated options</li>
                        <li>Copy your preferred username with one click</li>
                        <li>Generate again for more options</li>
                    </ul>
                </div>
            </section>

            <!-- FAQ Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">Email Username Generator FAQ</h2>
                <div class="space-y-6">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">How professional are the generated usernames?</h3>
                        <p class="text-gray-600">When using the professional style option, our generator creates business-appropriate usernames following established conventions for workplace email addresses.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Are these usernames available everywhere?</h3>
                        <p class="text-gray-600">While we generate unique combinations, availability varies by email provider. We recommend trying several options as some may already be taken on your preferred platform.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I use these for business email addresses?</h3>
                        <p class="text-gray-600">Yes! Select the "professional" style option to generate business-appropriate usernames that maintain a professional appearance.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">How many usernames can I generate?</h3>
                        <p class="text-gray-600">You can generate up to 20 unique usernames per request, and there's no limit to how many times you can generate new options.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Are the generated usernames secure?</h3>
                        <p class="text-gray-600">Our generator creates usernames that balance uniqueness with security, avoiding common patterns that could compromise your online safety.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">What makes a good email username?</h3>
                        <p class="text-gray-600">A good email username is memorable, professional when needed, easy to communicate, and avoids special characters that might cause confusion.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I customize the generated usernames?</h3>
                        <p class="text-gray-600">Yes, you can use our options to include numbers, dots, and underscores, allowing you to create usernames that match your preferences.</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>

    <script>
        async function generateEmails() {
            const nameInput = document.getElementById('nameInput').value.trim();
            const includeNumbers = document.getElementById('includeNumbers').checked;
            const includeDots = document.getElementById('includeDots').checked;
            const includeUnderscores = document.getElementById('includeUnderscores').checked;
            const style = document.getElementById('styleSelect').value;
            
            const emailList = document.getElementById('emailList');
            emailList.innerHTML = '<div class="col-span-full text-center text-gray-600">Generating email usernames...</div>';

            try {
                const prompt = `Generate 20 unique email usernames${nameInput ? ` based on: ${nameInput}` : ''}.
                    Style: ${style !== 'any' ? style : 'mixed'}.
                    ${includeNumbers ? 'Include some numbers.' : 'No numbers.'}
                    ${includeDots ? 'Use dots in some names.' : 'No dots.'}
                    ${includeUnderscores ? 'Use underscores in some names.' : 'No underscores.'}
                    Do not include @ or domain names.
                    Make them appropriate for email use.
                    Return only the usernames, one per line.`;

                const response = await fetch('https://randomlygenerate-connectai-151124.gattr.workers.dev/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        prompt,
                        temperature: 0.7,
                        max_tokens: 500
                    })
                });

                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || 'Failed to generate email usernames');
                }

                const emails = data.result
                    .split('\n')
                    .map(email => email.trim())
                    .filter(email => email && !email.includes('@') && !email.includes('```'));

                updateEmailDisplay(emails);
                
            } catch (error) {
                console.error('Error:', error);
                emailList.innerHTML = `
                    <div class="col-span-full text-center text-red-600">
                        <div>Error generating email usernames. Please try again.</div>
                        <div class="text-sm mt-2">${error.message}</div>
                    </div>`;
            }
        }

        function updateEmailDisplay(emails) {
            const emailList = document.getElementById('emailList');
            emailList.innerHTML = emails.map(email => `
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="font-mono text-sm break-all mr-2">${email}</div>
                    <button 
                        onclick="copyEmail('${email.replace(/'/g, "\\'")}')"
                        class="text-blue-600 hover:text-blue-800 text-sm ml-4 whitespace-nowrap"
                    >
                        Copy
                    </button>
                </div>
            `).join('');
        }

        async function copyEmail(email) {
            try {
                await navigator.clipboard.writeText(email);
                const tooltip = document.createElement('div');
                tooltip.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg';
                tooltip.textContent = 'Email username copied!';
                document.body.appendChild(tooltip);
                setTimeout(() => tooltip.remove(), 2000);
            } catch (err) {
                console.error('Failed to copy:', err);
                alert('Failed to copy email username');
            }
        }

        function clearEmails() {
            if (confirm('Are you sure you want to clear all email usernames?')) {
                document.getElementById('emailList').innerHTML = '';
                document.getElementById('nameInput').value = '';
                document.getElementById('styleSelect').value = 'any';
            }
        }
    </script>
</body>
</html> 