<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Random Password Generator | Secure Password Creator | RandomlyGenerate</title>
    <meta name="description" content="Create strong, secure random passwords instantly. Customize length, include special characters, numbers, and more. Free online password generator tool.">
    <meta name="keywords" content="password generator, random password, secure password, strong password creator, password tool">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="/assets/css/styles.css" rel="stylesheet">
</head>
<body class="flex flex-col min-h-screen">
    <header-component></header-component>

    <main class="container mx-auto px-4 py-8 flex-grow">
        <div class="max-w-4xl mx-auto">
            <article class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-6">
                    <h1 class="text-3xl font-bold mb-4">Random Password Generator</h1>
                    <p class="text-gray-600 mb-8">Generate secure random passwords with customizable options.</p>

                    <!-- Generator Card -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                        <!-- Password Options -->
                        <div class="space-y-4 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Password Length: <span id="lengthValue">16</span></label>
                                <input 
                                    type="range" 
                                    id="length" 
                                    min="8" 
                                    max="32" 
                                    value="16"
                                    class="w-full"
                                    oninput="document.getElementById('lengthValue').textContent = this.value"
                                >
                            </div>

                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <input type="checkbox" id="uppercase" class="mr-2" checked>
                                    <label for="uppercase" class="text-sm text-gray-700">Include Uppercase Letters</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="lowercase" class="mr-2" checked>
                                    <label for="lowercase" class="text-sm text-gray-700">Include Lowercase Letters</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="numbers" class="mr-2" checked>
                                    <label for="numbers" class="text-sm text-gray-700">Include Numbers</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="symbols" class="mr-2" checked>
                                    <label for="symbols" class="text-sm text-gray-700">Include Symbols</label>
                                </div>
                            </div>
                        </div>

                        <!-- Generate Button -->
                        <button 
                            onclick="generatePasswords()"
                            class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors font-bold text-sm"
                        >
                            Generate Passwords
                        </button>
                    </div>

                    <!-- Generated Passwords Section -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                        <h2 class="text-xl font-semibold mb-4">Generated Passwords</h2>
                        <div id="results" class="space-y-2">
                            <!-- Results will be added here -->
                        </div>
                    </div>

                    <!-- Password Tips -->
                    <section>
                        <h3 class="text-xl font-semibold mb-4 text-gray-700">Password Tips</h3>
                        <ul class="list-disc list-inside text-gray-600 space-y-2">
                            <li>Use at least 12 characters for better security</li>
                            <li>Mix different types of characters</li>
                            <li>Avoid personal information</li>
                            <li>Use unique passwords for each account</li>
                        </ul>
                    </section>
                </div>
            </article>
        </div>

        <!-- New SEO Content Sections -->
        <div class="max-w-4xl mx-auto mt-16 bg-[#e8f4f8] rounded-lg p-8">
            <!-- About Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">About Password Generation</h2>
                <p class="text-gray-600 leading-relaxed">
                    Password generation is a critical aspect of modern digital security, combining cryptographic principles with user-friendly functionality. Strong passwords serve as the first line of defense against unauthorized access, protecting everything from personal emails to corporate networks. Modern password generators use sophisticated algorithms to create complex combinations of characters that are both secure against brute-force attacks and dictionary-based hacking attempts. The increasing sophistication of cyber threats has made random password generation an essential tool for maintaining digital security across personal and professional contexts.
                </p>
            </section>

            <!-- History Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">History of Password Security</h2>
                <p class="text-gray-600 leading-relaxed">
                    The evolution of password security traces back to the early days of computing in the 1960s, when MIT's Compatible Time-Sharing System introduced the first computer password system. As digital systems became more prevalent, password requirements evolved from simple numeric codes to complex combinations of characters. The 1990s internet boom highlighted the need for stronger password practices, leading to the development of automated password generators. Modern password security incorporates lessons learned from decades of cybersecurity research, emphasizing randomness, length, and character diversity to create passwords that resist increasingly sophisticated hacking techniques.
                </p>
            </section>

            <!-- How to Use Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">How to Use Our Password Generator</h2>
                <div class="text-gray-600 space-y-4">
                    <p><strong>1. Customize Your Password:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Choose password length (8-128 characters)</li>
                        <li>Select character types (uppercase, lowercase, numbers, symbols)</li>
                        <li>Adjust settings based on specific requirements</li>
                    </ul>

                    <p><strong>2. Generate and Verify:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Click generate to create a new password</li>
                        <li>Check the strength indicator</li>
                        <li>Verify it meets your requirements</li>
                    </ul>

                    <p><strong>3. Save and Use:</strong></p>
                    <ul class="list-disc pl-6">
                        <li>Copy password with one click</li>
                        <li>View password history</li>
                        <li>Generate multiple options as needed</li>
                    </ul>
                </div>
            </section>

            <!-- FAQ Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-bold mb-6 text-gray-800">Password Generator FAQ</h2>
                <div class="space-y-6">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">How secure are the generated passwords?</h3>
                        <p class="text-gray-600">Our generator uses cryptographic-grade randomization to create highly secure passwords that are extremely resistant to brute-force attacks and pattern analysis.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Are my generated passwords stored anywhere?</h3>
                        <p class="text-gray-600">No, passwords are generated client-side and are only temporarily stored in your browser's local history. They're automatically cleared when you close the page.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">What makes a password strong?</h3>
                        <p class="text-gray-600">Strong passwords combine length (12+ characters), multiple character types, randomness, and uniqueness. Our generator automatically optimizes these factors.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I customize password requirements?</h3>
                        <p class="text-gray-600">Yes, you can specify length and choose which character types to include (uppercase, lowercase, numbers, symbols) to meet specific password requirements.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">How often should I generate new passwords?</h3>
                        <p class="text-gray-600">Security experts recommend changing passwords every 3-6 months, or immediately if there's any suspicion of compromise.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Can I generate multiple passwords at once?</h3>
                        <p class="text-gray-600">Yes, you can generate as many passwords as needed and view your recent generation history for comparison.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="font-semibold mb-2">Are these passwords compatible with all websites?</h3>
                        <p class="text-gray-600">Our generator creates passwords that meet most standard requirements, but you can customize the settings if a site has specific restrictions.</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <footer-component></footer-component>

    <!-- Load components -->
    <script src="/assets/js/components/header.js"></script>
    <script src="/assets/js/components/footer.js"></script>

    <script>
        async function generatePasswords() {
            const length = parseInt(document.getElementById('length').value);
            const useUppercase = document.getElementById('uppercase').checked;
            const useLowercase = document.getElementById('lowercase').checked;
            const useNumbers = document.getElementById('numbers').checked;
            const useSymbols = document.getElementById('symbols').checked;

            // Ensure at least one character type is selected
            if (!useUppercase && !useLowercase && !useNumbers && !useSymbols) {
                alert('Please select at least one character type');
                return;
            }

            const responseDiv = document.getElementById('results');
            responseDiv.innerHTML = '<div class="text-center text-gray-600">Generating passwords...</div>';

            try {
                const prompt = `Generate 20 passwords with the following criteria: 
                    Length: ${length}, 
                    Include Uppercase: ${useUppercase}, 
                    Include Lowercase: ${useLowercase}, 
                    Include Numbers: ${useNumbers}, 
                    Include Symbols: ${useSymbols}. 
                    Return only the passwords, one per line. No additional text or formatting.`;

                const response = await fetch('https://randomlygenerate-connectai-151124.gattr.workers.dev/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ prompt })
                });

                const data = await response.json();

                if (!data.success) {
                    throw new Error(data.error || 'Failed to generate passwords');
                }

                // Split result into lines and clean up
                const passwords = data.result
                    .split('\n')
                    .map(password => password.trim())
                    .filter(password => password);

                if (passwords.length === 0) {
                    throw new Error('No valid passwords generated');
                }

                // Update UI with all passwords
                updateResults(passwords);

            } catch (error) {
                console.error('Error:', error);
                responseDiv.innerHTML = `
                    <div class="text-center text-red-600">
                        <div>Error generating passwords. Please try again.</div>
                        <div class="text-sm mt-2">${error.message}</div>
                    </div>`;
            }
        }

        function updateResults(passwords) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = passwords.map(password => `
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                    <div class="font-mono">${password}</div>
                    <button 
                        onclick="copyToClipboard('', '${password.replace(/'/g, "\\'")}')"
                        class="text-blue-600 hover:text-blue-800 focus:outline-none px-3 py-1 text-sm"
                    >
                        Copy
                    </button>
                </div>
            `).join('');
        }

        function copyToClipboard(elementId = '', text = '') {
            let content = text;

            // Create temporary input for copying
            const tempInput = document.createElement('input');
            tempInput.value = content;
            document.body.appendChild(tempInput);
            tempInput.select();
            document.execCommand('copy');
            document.body.removeChild(tempInput);

            // Visual feedback
            const copyButton = event.target;
            const originalText = copyButton.textContent;
            copyButton.textContent = 'Copied!';
            setTimeout(() => {
                copyButton.textContent = originalText;
            }, 1000);
        }
    </script>
</body>
</html> 