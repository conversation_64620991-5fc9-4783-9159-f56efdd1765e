class ShareButtons extends HTMLElement {
    constructor() {
        super();
    }

    connectedCallback() {
        const currentUrl = encodeURIComponent(window.location.href);
        const title = encodeURIComponent(document.title);
        
        this.innerHTML = `
        <div class="flex space-x-4 items-center">
            <span class="text-gray-600">Share:</span>
            <a href="https://twitter.com/intent/tweet?url=${currentUrl}&text=${title}" 
               target="_blank" 
               class="text-blue-400 hover:text-blue-500">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <!-- Twitter/X icon SVG -->
                </svg>
            </a>
            <a href="https://www.facebook.com/sharer/sharer.php?u=${currentUrl}" 
               target="_blank" 
               class="text-blue-600 hover:text-blue-700">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <!-- Facebook icon SVG -->
                </svg>
            </a>
            <button onclick="copyToClipboard('${decodeURIComponent(currentUrl)}')"
                    class="text-gray-600 hover:text-gray-700">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <!-- Copy icon SVG -->
                </svg>
            </button>
        </div>
        `;
    }
}

customElements.define('share-buttons', ShareButtons);

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg';
        toast.textContent = 'Link copied to clipboard!';
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 3000);
    });
} 