class Footer extends HTMLElement {
    constructor() {
        super();
    }

    connectedCallback() {
        this.innerHTML = `
            <footer class="bg-gray-800 text-white py-8">
                <div class="container mx-auto px-4">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                        <!-- Quick Links -->
                        <div>
                            <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                            <ul class="space-y-2">
                                <li><a href="/" class="hover:text-gray-300">Home</a></li>
                                <li><a href="/pages/about.html" class="hover:text-gray-300">About</a></li>
                                <li><a href="/pages/contact.html" class="hover:text-gray-300">Contact</a></li>
                            </ul>
                        </div>

                        <!-- Popular Tools -->
                        <div>
                            <h3 class="text-lg font-semibold mb-4">Popular Tools</h3>
                            <ul class="space-y-2">
                                <li><a href="/generators/random-password-generator.html" class="hover:text-gray-300">Password Generator</a></li>
                                <li><a href="/generators/random-number.html" class="hover:text-gray-300">Number Generator</a></li>
                                <li><a href="/decision/spin-the-wheel.html" class="hover:text-gray-300">Spin the Wheel</a></li>
                            </ul>
                        </div>

                        <!-- More Tools -->
                        <div>
                            <h3 class="text-lg font-semibold mb-4">More Tools</h3>
                            <ul class="space-y-2">
                                <li><a href="/generators/random-username-generator.html" class="hover:text-gray-300">Username Generator</a></li>
                                <li><a href="/generators/random-color-generator.html" class="hover:text-gray-300">Color Generator</a></li>
                                <li><a href="/generators/random-fact-generator.html" class="hover:text-gray-300">Fact Generator</a></li>
                            </ul>
                        </div>

                        <!-- Contact Info -->
                        <div>
                            <h3 class="text-lg font-semibold mb-4">Contact Us</h3>
                            <ul class="space-y-2">
                                <li><a href="/pages/contact.html" class="hover:text-gray-300">Get in Touch</a></li>
                                <li><a href="mailto:<EMAIL>" class="hover:text-gray-300"><EMAIL></a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="mt-8 pt-8 border-t border-gray-700 text-center text-sm">
                        <p>&copy; ${new Date().getFullYear()} RandomlyGenerate. All rights reserved.</p>
                        <div class="mt-2">
                            <a href="/pages/terms.html" class="hover:text-gray-300">Terms of Service</a> | 
                            <a href="/pages/privacy.html" class="hover:text-gray-300">Privacy Policy</a>
                        </div>
                    </div>
                </div>
            </footer>
        `;
    }
}

customElements.define('footer-component', Footer); 