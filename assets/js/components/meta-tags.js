class MetaTags {
    static getDefault(title, description, path) {
        return `
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title} | RandomlyGenerate</title>
    <meta name="description" content="${description}">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://randomlygenerate.com${path}">
    <meta property="og:title" content="${title} | RandomlyGenerate">
    <meta property="og:description" content="${description}">
    <meta property="og:image" content="https://randomlygenerate.com/assets/images/og-default.jpg">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://randomlygenerate.com${path}">
    <meta name="twitter:title" content="${title} | RandomlyGenerate">
    <meta name="twitter:description" content="${description}">
    <meta name="twitter:image" content="https://randomlygenerate.com/assets/images/twitter-default.jpg">

    <link rel="canonical" href="https://randomlygenerate.com${path}">
        `;
    }
} 