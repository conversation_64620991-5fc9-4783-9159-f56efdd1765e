class Header extends HTMLElement { 
    constructor() { 
        super();
    }

    connectedCallback() {
        this.innerHTML = `
        <header class="bg-white shadow-sm border-b">
            <nav class="container mx-auto px-4 py-4">
                <div class="flex justify-between items-center">
                    <a href="/" class="text-xl font-bold text-gray-800">RandomlyGenerate</a>
                    
                    <!-- Mobile Menu Button (Hamburger) -->
                    <button id="mobile-menu-button" class="md:hidden text-gray-600 hover:text-gray-900 focus:outline-none">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>

                    <!-- Desktop Navigation -->
                    <div class="hidden md:flex space-x-6">
                        <a href="/" class="hover:text-gray-600 transition-colors">Home</a>
                        <a href="/#decision-tools" class="hover:text-gray-600 transition-colors">Decision Tools</a>
                        <a href="/#generator-tools" class="hover:text-gray-600 transition-colors">Random Generators</a>
                        <a href="/pages/about.html" class="hover:text-gray-600 transition-colors">About Us</a>
                    </div>
                </div>

                <!-- Mobile Navigation Menu -->
                <div id="mobile-menu" class="hidden absolute top-16 left-0 right-0 bg-white md:hidden border-b shadow-lg">
                    <div class="flex flex-col p-4 space-y-4">
                        <a href="/" class="text-lg hover:bg-blue-800 hover:text-white transition-colors p-2 rounded">Home</a>
                        <a href="/#decision-tools" class="text-lg hover:bg-blue-800 hover:text-white transition-colors p-2 rounded">Decision Tools</a>
                        <a href="/#generator-tools" class="text-lg hover:bg-blue-800 hover:text-white transition-colors p-2 rounded">Random Generators</a>
                        <a href="/pages/about.html" class="text-lg hover:bg-blue-800 hover:text-white transition-colors p-2 rounded">About Us</a>
                    </div>
                </div>
            </nav>
        </header>
        `;

        // Add tracking codes
        this.addTrackingCodes();

        // Add event listeners after the HTML is inserted
        this.initializeMenu();
    }

    addTrackingCodes() {
        // Google Analytics tracking code
        const trackingId = 'G-4MWJ5PB6RW'; // Updated tracking ID
        const gaScript1 = document.createElement('script');
        gaScript1.async = true;
        gaScript1.src = `https://www.googletagmanager.com/gtag/js?id=${trackingId}`;
        
        const gaScript2 = document.createElement('script');
        gaScript2.text = `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${trackingId}');
        `;

        document.head.appendChild(gaScript1);
        document.head.appendChild(gaScript2);

        // Umami tracking code as a single line
        const umamiScript = document.createElement('script');
        umamiScript.defer = true;
        umamiScript.src = "https://sites.leanalyticsmedia.com/script.js";
        umamiScript.setAttribute('data-website-id', '7dd34485-3f2e-4478-a55c-ac9ce4448757');

        document.head.appendChild(umamiScript);
    }

    initializeMenu() {
        const mobileMenuButton = this.querySelector('#mobile-menu-button');
        const mobileMenu = this.querySelector('#mobile-menu');

        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', () => {
                const isHidden = mobileMenu.classList.contains('hidden');
                mobileMenu.classList.toggle('hidden');
                
                // Log for debugging
                console.log('Menu button clicked, menu is now:', isHidden ? 'visible' : 'hidden');
            });

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!mobileMenu.contains(e.target) && 
                    !mobileMenuButton.contains(e.target) && 
                    !mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('hidden');
                }
            });

            // Close menu when screen size changes
            window.addEventListener('resize', () => {
                if (window.innerWidth >= 768) {
                    mobileMenu.classList.add('hidden');
                }
            });
        }
    }
}

customElements.define('header-component', Header); 