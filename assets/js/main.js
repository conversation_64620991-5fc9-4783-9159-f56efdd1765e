// Define the tools arrays by category
const decisionTools = [
    {
        title: "<PERSON><PERSON> Roller",
        description: "Roll up to 6 dice simultaneously. Perfect for games, random decisions, and more.",
        link: "/decision/dice-roller.html",
        icon: "🎲",
        gradient: "from-indigo-400 to-purple-600"
    },
    {
        title: "Flip A Coin",
        description: "Simple heads or tails coin flip simulator. Perfect for making quick decisions.",
        link: "/decision/flip-a-coin.html",
        icon: "🪙",
        gradient: "from-yellow-400 to-yellow-600" 
    },
    {
        title: "Spin the Wheel",
        description: "Create a custom wheel with your own items and spin to randomly select one. Perfect for making random selections and decisions.",
        link: "/decision/spin-the-wheel.html",
        icon: "🎡",
        gradient: "from-pink-500 to-rose-600"
    },
    {
        title: "Yes No Coin Flip",
        description: "Get instant Yes or No answers with our virtual coin flip. Great for quick binary decisions.",
        link: "/decision/yes-no-coin-flip.html",
        icon: "✓✗",
        gradient: "from-green-400 to-blue-500"
    }
];

// Sort decisionTools alphabetically by title
decisionTools.sort((a, b) => a.title.localeCompare(b.title));

const generatorTools = [
    {
        title: "Business Name Generator",
        description: "Create unique and memorable business names based on your description. Choose length preferences and get instant suggestions.",
        link: "/generators/business-name-generator.html",
        icon: "💼",
        gradient: "from-blue-500 to-cyan-600"
    },
    {
        title: "Random Animal Generator",
        description: "Generate random animals based on characteristics. Choose between fierce predators, herbivores, night hunters, and more.",
        link: "/generators/random-animal-generator.html",
        icon: "🦁",
        gradient: "from-red-500 to-pink-600"
    },
    {
        title: "Random Bible Verse Generator",
        description: "Generate inspiring Bible verses based on topics, emotions, or specific books. Includes context and multiple translations.",
        link: "/generators/random-bible-verse.html",
        icon: "📖",
        gradient: "from-yellow-500 to-amber-600"
    },
    {
        title: "Random Color Generator",
        description: "Generate beautiful color palettes and shades based on your color preferences. Get hex codes and preview swatches.",
        link: "/generators/random-color-generator.html",
        icon: "🎨",
        gradient: "from-pink-500 to-orange-500"
    },
    {
        title: "Random Email Username Generator",
        description: "Create unique email usernames for your accounts. Generate professional or casual email usernames without domains.",
        link: "/generators/random-email-generator.html",
        icon: "📧",
        gradient: "from-teal-400 to-emerald-500"
    },
    {
        title: "Random Fact Generator",
        description: "Generate interesting random facts about various topics. Learn something new with every click!",
        link: "/generators/random-fact-generator.html",
        icon: "💡",
        gradient: "from-blue-400 to-cyan-500"
    },
    {
        title: "Random Motivational Quote Generator",
        description: "Generate inspiring quotes based on themes. Include authors and context for added depth and meaning.",
        link: "/generators/random-motivational-quote.html",
        icon: "💫",
        gradient: "from-indigo-500 to-purple-600"
    },
    {
        title: "Random Name Generator",
        description: "Generate random English names. Choose between masculine, feminine, or any names.",
        link: "/generators/random-name-generator.html",
        icon: "👤",
        gradient: "from-violet-500 to-purple-600"
    },
    {
        title: "Random Number Generator",
        description: "Generate random numbers within any range. Perfect for games, decisions, or any situation needing a random number.",
        link: "/generators/random-number.html",
        icon: "🎲",
        gradient: "from-purple-500 to-indigo-600"
    },
    {
        title: "Random Password Generator",
        description: "Generate secure random passwords with customizable length and character types. Includes strength indicator and history.",
        link: "/generators/random-password-generator.html",
        icon: "🔒",
        gradient: "from-green-500 to-emerald-600"
    },
    {
        title: "Random Sentence Generator",
        description: "Generate creative and unique sentences based on topics. Choose between simple or complex structures with emotional context.",
        link: "/generators/random-sentence-generator.html",
        icon: "✍️",
        gradient: "from-teal-500 to-cyan-600"
    },
    {
        title: "Random Username Generator",
        description: "Create unique usernames instantly. Perfect for gaming, social media, or any online presence.",
        link: "/generators/random-username-generator.html",
        icon: "👥",
        gradient: "from-orange-500 to-amber-600"
    }
];

// Sort generatorTools alphabetically by title
generatorTools.sort((a, b) => a.title.localeCompare(b.title));

// Function to create a card element
function createCard(tool) {
    return `
        <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
            <a href="${tool.link}" class="block">
                <div class="h-48 bg-gradient-to-br ${tool.gradient} flex items-center justify-center">
                    <span class="text-4xl font-bold text-white">${tool.icon}</span>
                </div>
                <div class="p-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-2">${tool.title}</h3>
                    <p class="text-gray-600">${tool.description}</p>
                    <div class="mt-4 flex items-center text-blue-500 hover:text-blue-600">
                        Try it now
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </div>
            </a>
        </div>
    `;
}

// Function to render the tools grids
function renderTools(searchTerm = '') {
    const decisionGrid = document.getElementById('decision-tools-grid');
    const generatorGrid = document.getElementById('generator-tools-grid');

    if (decisionGrid && generatorGrid) {
        // Filter tools based on search term
        const filteredDecisionTools = decisionTools.filter(tool => 
            tool.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            tool.description.toLowerCase().includes(searchTerm.toLowerCase())
        );

        const filteredGeneratorTools = generatorTools.filter(tool => 
            tool.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            tool.description.toLowerCase().includes(searchTerm.toLowerCase())
        );

        // Render decision tools
        decisionGrid.innerHTML = filteredDecisionTools.map(tool => createCard(tool)).join('');

        // Render generator tools
        generatorGrid.innerHTML = filteredGeneratorTools.map(tool => createCard(tool)).join('');
    }
}

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    // Initial render
    renderTools();

    // Setup search functionality
    const searchInput = document.getElementById('search');
    if (searchInput) {
        searchInput.addEventListener('input', (e) => {
            renderTools(e.target.value);
        });
    }
}); 