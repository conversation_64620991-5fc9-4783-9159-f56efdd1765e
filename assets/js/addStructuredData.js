document.addEventListener('DOMContentLoaded', () => {
    const structuredData = {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "RandomlyGenerate",
        "url": "https://randomlygenerate.com/",
        "description": "Free collection of random generators and decision-making tools.",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "https://randomlygenerate.com/?search={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    };

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.text = JSON.stringify(structuredData);
    
    document.head.appendChild(script);
});
