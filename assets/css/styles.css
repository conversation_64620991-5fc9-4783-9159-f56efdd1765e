/* Add any custom styles here */
body {
    background-color: white !important;
}

/* Add fixed card height */
.utility-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.utility-card-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.utility-card-description {
    flex-grow: 1;
}

/* Add any additional custom styles */

/* Mobile menu transitions */
#mobile-menu {
    transition: all 0.3s ease-in-out;
    z-index: 50;
}

#mobile-menu:not(.hidden) {
    animation: slideDown 0.3s ease-out;
}

/* Prevent scrolling when mobile menu is open */
body.overflow-hidden {
    overflow: hidden;
}

/* Optional: Add slide-in animation */
@keyframes slideIn {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

#mobile-menu:not(.hidden) .flex-col {
    animation: slideIn 0.3s ease-out;
}

/* Mobile menu styles */
#mobile-menu {
    transition: all 0.3s ease-in-out;
    z-index: 50;
}

/* Optional: Add animation */
@keyframes slideDown {
    from {
        transform: translateY(-10px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

#mobile-menu:not(.hidden) {
    animation: slideDown 0.3s ease-out;
}
 