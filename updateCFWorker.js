const fs = require('fs').promises;
const path = require('path');

// Define the directory containing your HTML files
const directoryPath = path.join(__dirname, 'generators');

// Define the old and new Cloudflare Worker URLs
const oldWorkerUrl = 'https://randomchoices-121124.gattr.workers.dev';
const newWorkerUrl = 'https://randomlygenerate-connectai-151124.gattr.workers.dev';

// Function to check if directory exists
async function ensureDirectoryExists(dirPath) {
    try {
        await fs.access(dirPath);
        console.log(`Directory exists: ${dirPath}`);
        return true;
    } catch (err) {
        console.error(`Directory does not exist: ${dirPath}`);
        return false;
    }
}

// Function to list and validate HTML files
async function listAndValidateHtmlFiles(dirPath) {
    try {
        // Check if directory exists first
        const directoryExists = await ensureDirectoryExists(dirPath);
        if (!directoryExists) {
            throw new Error(`Cannot proceed: Directory ${dirPath} does not exist`);
        }

        // Read directory contents
        const files = await fs.readdir(dirPath);

        // Filter and validate HTML files
        const htmlFiles = [];
        for (const file of files) {
            const filePath = path.join(dirPath, file);
            const stats = await fs.stat(filePath);

            if (stats.isFile() && path.extname(file).toLowerCase() === '.html') {
                try {
                    // Attempt to read the file
                    await fs.access(filePath, fs.constants.R_OK);
                    htmlFiles.push({
                        filename: file,
                        path: filePath
                    });
                } catch (accessErr) {
                    console.error(`Cannot read file: ${filePath}`);
                }
            }
        }

        // Log findings
        console.log(`Found ${htmlFiles.length} HTML files:`);
        htmlFiles.forEach(file => console.log(file.filename));

        return htmlFiles;
    } catch (err) {
        console.error('Error listing HTML files:', err);
        return [];
    }
}

// Function to update the worker URL in HTML files
async function updateWorkerUrlInFiles() {
    try {
        // First, validate the directory and files
        const htmlFiles = await listAndValidateHtmlFiles(directoryPath);

        if (htmlFiles.length === 0) {
            console.warn('No HTML files found to process.');
            return;
        }

        // Process each HTML file
        for (const file of htmlFiles) {
            try {
                // Read the file content
                const data = await fs.readFile(file.path, 'utf8');

                // Check if the file contains the old worker URL
                if (data.includes(oldWorkerUrl)) {
                    // Replace the old worker URL with the new one
                    const updatedData = data.replace(new RegExp(oldWorkerUrl, 'g'), newWorkerUrl);

                    // Write the updated content back to the file
                    await fs.writeFile(file.path, updatedData, 'utf8');
                    console.log(`Updated ${file.filename} with new worker URL.`);
                } else {
                    console.log(`No changes needed for ${file.filename}.`);
                }
            } catch (fileErr) {
                console.error(`Error processing file ${file.filename}:`, fileErr);
            }
        }

        console.log('URL replacement process completed.');
    } catch (err) {
        console.error('Error in updateWorkerUrlInFiles:', err);
    }
}

// Run the function to update the worker URLs
updateWorkerUrlInFiles();
